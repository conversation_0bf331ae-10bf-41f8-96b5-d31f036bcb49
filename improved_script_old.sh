#!/bin/bash
# 获取基站数据下载脚本
# 描述: 通过SFTP从多台服务器下载5G基站的MR数据

# 严格模式设置
set -euo pipefail

# 多线程设置
MAX_PARALLEL_DOWNLOADS=10  # 最大并行下载数
# 日期设置
current_date=20250420
# 下载文件名设置
ARCHIVE_NAME="5G基站PCIMR-${current_date}.tar.gz"

# 日志文件
LOG_FILE="download_log.txt"
STATS_DIR="/tmp/sftp_stats_$$"  # 临时目录存储统计信息，使用PID确保唯一性
mkdir -p "$STATS_DIR"

# 进度显示
show_progress() {
  local completed=$1
  local total=$2
  local percent=$((completed * 100 / total))
  echo -ne "下载进度: ${percent}% (${completed}/${total})\r"
}

# 错误处理函数
handle_error() {
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] 错误发生在第 $1 行: $2" >> "$LOG_FILE"
  exit 1
}

# 设置错误处理
trap 'handle_error ${LINENO} "$BASH_COMMAND"' ERR

# 清理临时文件
cleanup() {
  rm -rf "$STATS_DIR"
}
trap cleanup EXIT

# 定义检查运行中作业的函数
check_running_jobs() {
  local running=0
  
  # 检查是否有任何任务运行中
  for job_to_check in $(cat "$TASK_TRACKER" 2>/dev/null || echo ""); do
    if ps -p "$job_to_check" > /dev/null 2>&1; then
      running=$((running + 1))
    elif [[ -f "$STATS_DIR/done_${job_to_check}" ]]; then
      # 任务已完成，从跟踪器中移除
      sed -i "/^${job_to_check}$/d" "$TASK_TRACKER" 2>/dev/null || true
      completed_jobs=$((completed_jobs + 1))
      show_progress $completed_jobs $total_tasks
    else
      # 任务不存在但没有完成标记，可能异常终止
      echo "[$(date '+%Y-%m-%d %H:%M:%S')] 警告: 任务 $job_to_check 可能异常终止" >> "$LOG_FILE"
      sed -i "/^${job_to_check}$/d" "$TASK_TRACKER" 2>/dev/null || true
      completed_jobs=$((completed_jobs + 1))
      show_progress $completed_jobs $total_tasks
    fi
  done
  
  running_jobs=$running
  
  # 定期打印当前状态到日志
  if (( SECONDS % 30 == 0 )); then
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 状态: 运行中任务=$running_jobs, 已完成任务=$completed_jobs, 总任务=$total_tasks" >> "$LOG_FILE"
  fi

  sleep 1
}

# 配置部分
# 可以使用当前日期，或指定日期
# current_date=$(date +%Y%m%d)
current_date=20250420
ARCHIVE_NAME="5G基站PCIMR-${current_date}.tar.gz"

# 设置SFTP账号和密码
USER="ftpuser"
PASSWORDLIST=("Mk@83cka" "Mk@83cka23")

# 目标目录(本地目录)
SRCDIR="/mnt/data/mr_get/5G基站PCIMR-$current_date"
mkdir -p "$SRCDIR"

# FTP目录(服务器文件目录)
DESDIRLIST=("/export/home/<USER>/internalftp/var/SauService/filter/TSInventory/gnodeb_sig")

echo "[$(date '+%Y-%m-%d %H:%M:%S')] 开始下载日期: $current_date 的数据" | tee -a "$LOG_FILE"

# 清理站点名称的函数，移除回车符和其他特殊字符
sanitize_station_name() {
  local station_name="$1"
  # 移除回车符(\r)和其他可能导致路径问题的特殊字符
  echo "$station_name" | tr -d '\r' | sed 's/[^[:alnum:][:space:]一-龥]/_/g'
}

# 清理IP地址的函数，确保只包含数字和点
sanitize_ip() {
  local ip="$1"
  # 先移除回车符(\r)，然后只保留IP地址中的数字和点
  echo "$ip" | tr -d '\r' | sed 's/[^0-9\.]//g'
}

# 检查是否有节点列表文件
NODEB_LIST_FILE="nodeb_list.txt"
if [[ -f "$NODEB_LIST_FILE" ]]; then
  # 读取节点列表文件，忽略空行和注释行，并处理特殊字符
  mapfile -t raw_nodeb_list < <(grep -v '^\s*#\|^\s*$' "$NODEB_LIST_FILE")
  Nodeb_set=()
  for nodeb in "${raw_nodeb_list[@]}"; do
    # 移除回车符并净化站点名称
    sanitized_nodeb=$(sanitize_station_name "$nodeb")
    Nodeb_set+=("$sanitized_nodeb")
    if [[ "$nodeb" != "$sanitized_nodeb" ]]; then
      echo "[$(date '+%Y-%m-%d %H:%M:%S')] 站点名称已清理: '$nodeb' -> '$sanitized_nodeb'" >> "$LOG_FILE"
    fi
  done
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] 从文件加载了 ${#Nodeb_set[@]} 个基站节点" >> "$LOG_FILE"
else
  # 这里可以放原脚本的基站列表，但为了简洁只保留注释说明
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] 未找到节点列表文件，使用脚本内置的基站列表" >> "$LOG_FILE"
  # 原始基站列表，在这里简化为示例
  raw_nodeb_set=(成都北HR04H 武侯HR03H 锦江HR03H 成青羊HR04H)
  # 清理内置站点名称
  Nodeb_set=()
  for nodeb in "${raw_nodeb_set[@]}"; do
    sanitized_nodeb=$(sanitize_station_name "$nodeb")
    Nodeb_set+=("$sanitized_nodeb")
  done
fi

# SFTP服务器IP列表文件
SERVER_LIST_FILE="server_list.txt"
if [[ -f "$SERVER_LIST_FILE" ]]; then
  # 读取服务器列表文件，忽略空行和注释行
  mapfile -t raw_ip_list < <(grep -v '^\s*#\|^\s*$' "$SERVER_LIST_FILE")
  IPLIST=()
  for ip in "${raw_ip_list[@]}"; do
    # 清理IP地址
    sanitized_ip=$(sanitize_ip "$ip")
    IPLIST+=("$sanitized_ip")
    if [[ "$ip" != "$sanitized_ip" ]]; then
      echo "[$(date '+%Y-%m-%d %H:%M:%S')] IP地址已清理: '$ip' -> '$sanitized_ip'" >> "$LOG_FILE"
    fi
  done
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] 从文件加载了 ${#IPLIST[@]} 个服务器IP" >> "$LOG_FILE"
else
  # 如果没有服务器列表文件，使用默认列表
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] 未找到服务器列表文件，使用脚本内置的服务器列表" >> "$LOG_FILE"
  # 原始IP列表，在这里简化为示例
  raw_ip_list=(************* ************* ************* *************)
  # 清理内置IP地址
  IPLIST=()
  for ip in "${raw_ip_list[@]}"; do
    sanitized_ip=$(sanitize_ip "$ip")
    IPLIST+=("$sanitized_ip")
  done
fi

# 检查重复的IP地址
IPLIST=($(echo "${IPLIST[@]}" | tr ' ' '\n' | sort -u | tr '\n' ' '))
echo "[$(date '+%Y-%m-%d %H:%M:%S')] 优化后的服务器IP数量: ${#IPLIST[@]}" >> "$LOG_FILE"

# 端口
PORT=22

# 连接超时时间(秒)
TIMEOUT=300

# 成功标记文件
SUCCESS_FILE="$SRCDIR/download_success.txt"
touch "$SUCCESS_FILE"

# 下载函数，减少重复代码
download_data() {
  local job_id=$1
  local ip=$2
  local password=$3
  local desdir=$4
  local nodeb_dir=$5
  local target_path="$current_date"
  local result_file="$STATS_DIR/result_${job_id}"
  
  # 初始化结果文件为空
  : > "$result_file"
  
  # 清理IP地址和站点名称
  ip=$(sanitize_ip "$ip")
  
  if [[ -n "$nodeb_dir" ]]; then
    nodeb_dir=$(sanitize_station_name "$nodeb_dir")
    target_path="$current_date/$nodeb_dir"
  fi
  
  # 检查是否已经成功下载过
  if grep -q "${ip}_${target_path}" "$SUCCESS_FILE" 2>/dev/null; then
    echo "SKIP:$job_id:$ip:$target_path" > "$result_file"
    touch "$STATS_DIR/done_${job_id}"
    return 0
  fi
  
  # 创建本地目录
  mkdir -p "$SRCDIR/$target_path"
  
  # 记录下载信息到日志
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] 开始下载: 服务器=$ip, 路径=$target_path" >> "$LOG_FILE"
  
  # 测试连接
  if ! timeout $((TIMEOUT / 2)) lftp -u "$USER,$password" "sftp://${ip}:${PORT}" -e "exit" 2>/dev/null; then
    echo "CONN_FAIL:$job_id:$ip" > "$result_file"
    touch "$STATS_DIR/done_${job_id}"
    return 1
  fi
  
  # 设置超时并进行下载
  if timeout $TIMEOUT lftp -u "$USER,$password" "sftp://${ip}:${PORT}" 2>/dev/null <<EOF; then
  set sftp:connect-program "ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null"
  set net:timeout $TIMEOUT
  set net:max-retries 2
  set net:reconnect-interval-base 2
  cd "$desdir"
  lcd "$SRCDIR"
  ls -la "$target_path" >> "$LOG_FILE" 2>&1
  echo "执行mirror命令: mirror --only-newer --parallel=5 '$target_path/' '$target_path'" >> "$LOG_FILE" 2>&1
  mirror --only-newer --parallel=5 --verbose "$target_path/" "$target_path" >> "$LOG_FILE" 2>&1
  close
  quit
EOF
    # 检查是否有文件被下载
    if [[ -z "$(find "$SRCDIR/$target_path" -type f 2>/dev/null)" ]]; then
      echo "[$(date '+%Y-%m-%d %H:%M:%S')] 警告: 目录 $SRCDIR/$target_path 下载后为空" >> "$LOG_FILE"
      echo "EMPTY:$job_id:$ip:$target_path" > "$result_file"
      touch "$STATS_DIR/done_${job_id}"
      return 1
    fi
    
    # 记录成功
    echo "${ip}_${target_path}" >> "$SUCCESS_FILE"
    echo "SUCCESS:$job_id:$ip:$target_path" > "$result_file"
    touch "$STATS_DIR/done_${job_id}"
    return 0
  else
    # 记录失败
    echo "FAIL:$job_id:$ip:$target_path" > "$result_file"
    touch "$STATS_DIR/done_${job_id}"
    return 1
  fi
}

# 维护有效连接的服务器列表
valid_servers=()

# 测试服务器连接有效性
echo "正在测试服务器连接..."
for IP in "${IPLIST[@]}"; do
  # 确保IP地址已清理
  IP=$(sanitize_ip "$IP")
  for PASSWORD in "${PASSWORDLIST[@]}"; do
    if timeout 5 lftp -u "$USER,$PASSWORD" "sftp://${IP}:${PORT}" -e "exit" 2>/dev/null; then
      valid_servers+=("$IP:$PASSWORD")
      echo "[$(date '+%Y-%m-%d %H:%M:%S')] 服务器连接成功: $IP" >> "$LOG_FILE"
      break  # 找到有效密码就跳出
    fi
  done
done

if [[ ${#valid_servers[@]} -eq 0 ]]; then
  echo "错误: 没有任何可访问的服务器。请检查网络或凭证。" | tee -a "$LOG_FILE"
  exit 1
fi

echo "有效服务器数量: ${#valid_servers[@]}"

# 计算总任务数
total_tasks=0
for server_info in "${valid_servers[@]}"; do
  IFS=':' read -r IP PASSWORD <<< "$server_info"
  # 确保IP地址已清理
  IP=$(sanitize_ip "$IP")
  for DESDIR in "${DESDIRLIST[@]}"; do
    if [[ ${#Nodeb_set[@]} -eq 0 ]]; then
      total_tasks=$((total_tasks + 1))
    else
      total_tasks=$((total_tasks + ${#Nodeb_set[@]}))
    fi
  done
done

if [[ $total_tasks -eq 0 ]]; then
  echo "错误: 没有可执行的下载任务。" | tee -a "$LOG_FILE"
  exit 1
fi

echo "总计划下载任务数: $total_tasks"

# 主下载逻辑
job_id=0
running_jobs=0
completed_jobs=0
SECONDS=0  # 用于跟踪脚本运行时间

# 创建任务跟踪文件
TASK_TRACKER="$STATS_DIR/task_tracker"
: > "$TASK_TRACKER"

# 对每个有效服务器进行下载
for server_info in "${valid_servers[@]}"; do
  IFS=':' read -r IP PASSWORD <<< "$server_info"
  # 确保IP地址已清理
  IP=$(sanitize_ip "$IP")
  
  for DESDIR in "${DESDIRLIST[@]}"; do
    # 根据Nodeb_set是否为空决定下载方式
    if [[ ${#Nodeb_set[@]} -eq 0 ]]; then
      # 等待可用线程
      while [[ $running_jobs -ge $MAX_PARALLEL_DOWNLOADS ]]; do
        check_running_jobs
      done
      
      # 启动下载任务
      job_id=$((job_id + 1))
      (
        download_data "$job_id" "$IP" "$PASSWORD" "$DESDIR" ""
      ) &
      pid=$!
      echo "$pid" >> "$TASK_TRACKER"
      running_jobs=$((running_jobs + 1))
      echo "[$(date '+%Y-%m-%d %H:%M:%S')] 启动任务 $job_id (PID: $pid): 服务器=$IP, 路径=$current_date" >> "$LOG_FILE"
      
    else
      # 只下载指定的基站
      for NODEB_DIR in "${Nodeb_set[@]}"; do
        # 等待可用线程
        while [[ $running_jobs -ge $MAX_PARALLEL_DOWNLOADS ]]; do
          check_running_jobs
        done
        
        # 确保站点名称已清理
        NODEB_DIR=$(sanitize_station_name "$NODEB_DIR")
        
        # 启动下载任务
        job_id=$((job_id + 1))
        (
          download_data "$job_id" "$IP" "$PASSWORD" "$DESDIR" "$NODEB_DIR"
        ) &
        pid=$!
        echo "$pid" >> "$TASK_TRACKER"
        running_jobs=$((running_jobs + 1))
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] 启动任务 $job_id (PID: $pid): 服务器=$IP, 路径=$current_date/$NODEB_DIR" >> "$LOG_FILE"
      done
    fi
  done
done

# 等待所有任务完成
echo "等待所有下载任务完成..."
# 设置最大等待时间为2小时
MAX_WAIT_SECONDS=$((2*60*60))
START_WAIT_TIME=$SECONDS

while [[ $completed_jobs -lt $total_tasks ]]; do
  check_running_jobs
  
  # 检查是否超过最大等待时间
  if (( SECONDS - START_WAIT_TIME > MAX_WAIT_SECONDS )); then
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 警告: 等待时间超过2小时，强制结束等待" | tee -a "$LOG_FILE"
    break
  fi
  
  # 如果没有正在运行的任务但任务未完成，可能是因为有任务未被捕获
  if [[ $running_jobs -eq 0 && $completed_jobs -lt $total_tasks ]]; then
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 警告：所有任务已结束，但统计数量不匹配 (已完成: $completed_jobs, 总计: $total_tasks)" | tee -a "$LOG_FILE"
    # 等待5秒再次检查，避免误判
    sleep 5
    check_running_jobs
    
    # 如果仍然没有运行中的任务，则强制退出循环
    if [[ $running_jobs -eq 0 ]]; then
      echo "[$(date '+%Y-%m-%d %H:%M:%S')] 确认所有任务已结束，强制完成" | tee -a "$LOG_FILE" 
      completed_jobs=$total_tasks  # 强制退出循环
    fi
  fi
done

# 确保终止所有可能仍在运行的下载任务
if [[ -s "$TASK_TRACKER" ]]; then
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] 终止剩余的下载任务..." | tee -a "$LOG_FILE"
  for pid in $(cat "$TASK_TRACKER"); do
    if ps -p $pid > /dev/null 2>&1; then
      echo "[$(date '+%Y-%m-%d %H:%M:%S')] 终止进程: $pid" >> "$LOG_FILE"
      kill $pid 2>/dev/null || true
    fi
  done
fi

# 等待所有后台进程完成
wait

echo -e "\n所有下载任务已完成 (${completed_jobs}/${total_tasks})"
echo "[$(date '+%Y-%m-%d %H:%M:%S')] 下载阶段耗时: $SECONDS 秒" >> "$LOG_FILE"

# 统计结果
echo "[$(date '+%Y-%m-%d %H:%M:%S')] 计算下载统计结果..." | tee -a "$LOG_FILE"

# 确保所有结果文件已写入
sleep 2

# 列出所有结果文件到日志以便调试
echo "[$(date '+%Y-%m-%d %H:%M:%S')] 结果文件列表:" >> "$LOG_FILE"
ls -la "$STATS_DIR"/result_* >> "$LOG_FILE" 2>&1 || echo "没有找到结果文件" >> "$LOG_FILE"

# 计算各种结果类型的数量
if find "$STATS_DIR" -name "result_*" | grep -q .; then
  success_count=$(grep -c "^SUCCESS:" "$STATS_DIR"/result_* 2>/dev/null || echo 0)
  fail_count=$(grep -c "^FAIL:" "$STATS_DIR"/result_* 2>/dev/null || echo 0)
  skip_count=$(grep -c "^SKIP:" "$STATS_DIR"/result_* 2>/dev/null || echo 0)
  conn_fail_count=$(grep -c "^CONN_FAIL:" "$STATS_DIR"/result_* 2>/dev/null || echo 0)
  empty_count=$(grep -c "^EMPTY:" "$STATS_DIR"/result_* 2>/dev/null || echo 0)
else
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] 错误: 未找到任何结果文件" | tee -a "$LOG_FILE"
  # 尝试从日志文件重建统计数据
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] 尝试从日志文件重建统计数据..." | tee -a "$LOG_FILE"
  success_count=$(grep -c "SUCCESS:" "$LOG_FILE" 2>/dev/null || echo 0)
  fail_count=$(grep -c "FAIL:" "$LOG_FILE" 2>/dev/null || echo 0)
  skip_count=$(grep -c "SKIP:" "$LOG_FILE" 2>/dev/null || echo 0)
  conn_fail_count=$(grep -c "CONN_FAIL:" "$LOG_FILE" 2>/dev/null || echo 0)
  empty_count=$(grep -c "EMPTY:" "$LOG_FILE" 2>/dev/null || echo 0)
fi

echo "下载统计:"
echo "  成功: $success_count"
echo "  失败: $fail_count"
echo "  连接失败: $conn_fail_count"
echo "  空目录: $empty_count"
echo "  跳过: $skip_count"
echo "  总计: $((success_count + fail_count + skip_count + conn_fail_count + empty_count))"

# 将统计结果也记录到日志
echo "[$(date '+%Y-%m-%d %H:%M:%S')] 下载统计:" >> "$LOG_FILE"
echo "  成功: $success_count" >> "$LOG_FILE"
echo "  失败: $fail_count" >> "$LOG_FILE"
echo "  连接失败: $conn_fail_count" >> "$LOG_FILE"
echo "  空目录: $empty_count" >> "$LOG_FILE"
echo "  跳过: $skip_count" >> "$LOG_FILE"
echo "  总计: $((success_count + fail_count + skip_count + conn_fail_count + empty_count))" >> "$LOG_FILE"

# 如果没有成功的下载且不是所有都跳过，则退出
if [[ $success_count -eq 0 && $skip_count -ne $total_tasks ]]; then
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] 错误: 没有成功的下载，退出" | tee -a "$LOG_FILE"
  exit 1
fi

# 压缩下载的文件
echo "[$(date '+%Y-%m-%d %H:%M:%S')] 开始压缩文件: $ARCHIVE_NAME" | tee -a "$LOG_FILE"

# 检查源目录是否存在且不为空
if [[ ! -d "$SRCDIR" || -z "$(ls -A "$SRCDIR" 2>/dev/null)" ]]; then
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] 错误: 源目录不存在或为空，无法压缩" | tee -a "$LOG_FILE"
  exit 1
fi

tar -czf "$ARCHIVE_NAME" -C "$(dirname "$SRCDIR")" "$(basename "$SRCDIR")"

if [[ $? -eq 0 ]]; then
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] 压缩成功: $ARCHIVE_NAME" | tee -a "$LOG_FILE"
  
  # 计算MD5校验和
  md5sum "$ARCHIVE_NAME" > "${ARCHIVE_NAME}.md5"
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] 生成MD5校验和: ${ARCHIVE_NAME}.md5" | tee -a "$LOG_FILE"
  
  # 询问是否删除原始文件
  read -p "是否删除已压缩的原始文件? (y/n): " -n 1 -r
  echo
  if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 删除原始文件: $SRCDIR" | tee -a "$LOG_FILE"
    rm -rf "$SRCDIR"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 删除完成" | tee -a "$LOG_FILE"
  else
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 保留原始文件" | tee -a "$LOG_FILE"
  fi
else
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] 压缩失败" | tee -a "$LOG_FILE"
fi

echo "[$(date '+%Y-%m-%d %H:%M:%S')] 脚本执行完成" | tee -a "$LOG_FILE" 