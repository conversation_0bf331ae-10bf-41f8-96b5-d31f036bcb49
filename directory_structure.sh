#!/bin/bash
# 目录结构保持机制

# 保存目录结构信息
save_directory_structure() {
  local base_dir="$1"
  local structure_file="$base_dir/directory_structure.txt"
  
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] 保存目录结构信息到: $structure_file"
  
  # 创建基础目录
  mkdir -p "$base_dir"
  
  # 保存目录结构
  find "$base_dir" -type d | sort > "$structure_file"
  
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] 目录结构已保存"
}

# 恢复目录结构
restore_directory_structure() {
  local base_dir="$1"
  local structure_file="$base_dir/directory_structure.txt"
  
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] 尝试恢复目录结构"
  
  # 检查结构文件是否存在
  if [[ ! -f "$structure_file" ]]; then
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 目录结构文件不存在，无法恢复"
    return 1
  fi
  
  # 恢复目录结构
  while read -r dir; do
    if [[ ! -d "$dir" ]]; then
      mkdir -p "$dir"
      echo "[$(date '+%Y-%m-%d %H:%M:%S')] 创建目录: $dir"
    fi
  done < "$structure_file"
  
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] 目录结构已恢复"
  return 0
}

# 测试函数
test_directory_structure() {
  local test_dir="./test_structure"
  
  # 创建测试目录结构
  mkdir -p "$test_dir/dir1/subdir1"
  mkdir -p "$test_dir/dir2/subdir2"
  
  # 保存目录结构
  save_directory_structure "$test_dir"
  
  # 删除部分目录
  rm -rf "$test_dir/dir1"
  
  # 恢复目录结构
  restore_directory_structure "$test_dir"
  
  # 验证
  if [[ -d "$test_dir/dir1/subdir1" ]]; then
    echo "测试成功: 目录结构已恢复"
  else
    echo "测试失败: 目录结构未恢复"
  fi
  
  # 清理
  rm -rf "$test_dir"
}

# 运行测试
test_directory_structure
