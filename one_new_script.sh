

# 严格模式设置
set -euo pipefail

# 日志函数
log() {
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a download_log.txt
}

# 错误处理函数
handle_error() {
  log "错误发生在第 $1 行: $2"
  exit 1
}

# 设置错误处理
trap 'handle_error ${LINENO} "$BASH_COMMAND"' ERR

# 配置部分
# 可以使用当前日期，或指定日期
# current_date=$(date +%Y%m%d)
current_date=20250420

# 目标目录(本地目录)
SRCDIR="/home/<USER>/MR_get/5G基站PCIMR-$current_date"
mkdir -p "$SRCDIR"

# FTP目录(服务器文件目录)
DESDIRLIST=("/export/home/<USER>/internalftp/var/SauService/filter/TSInventory/gnodeb_sig")

log "开始下载日期: $current_date 的数据"

# 清理站点名称的函数
sanitize_station_name() {
  local station_name="$1"
  echo "$station_name" | tr -d '\r\n' | sed 's/[^[:alnum:][:space:]一-龥]/_/g'
}

# 检查是否有节点列表文件
NODEB_LIST_FILE="nodeb_list.txt"
if [[ -f "$NODEB_LIST_FILE" ]]; then
  # 从文件读取站点列表，忽略注释和空行
  raw_nodeb_list=($(grep -v '^\s*#\|^\s*$' "$NODEB_LIST_FILE"))

  # 清理站点名称
  Nodeb_set=()
  for nodeb in "${raw_nodeb_list[@]}"; do
    clean_nodeb=$(sanitize_station_name "$nodeb")
    Nodeb_set+=("$clean_nodeb")
  done

  log "从文件加载并清理了 ${#Nodeb_set[@]} 个基站节点"
else
  # 这里保留原脚本的基站列表，但为了简洁只保留注释说明
  log "使用脚本内置的基站列表"
  # 初始化为空数组
  Nodeb_set=()
  # 如果需要手动输入基站列表，可以取消下面的注释
  # echo "请手动输入基站列表，使用空格隔开："
  # read -r -a raw_nodeb_list
  # 清理站点名称
  # for nodeb in "${raw_nodeb_list[@]}"; do
  #   clean_nodeb=$(sanitize_station_name "$nodeb")
  #   Nodeb_set+=("$clean_nodeb")
  # done
fi

# SFTP服务器IP列表文件
SERVER_LIST_FILE="server_list.txt"
log "尝试从 $SERVER_LIST_FILE 文件加载服务器IP列表"

# 清理IP地址的函数
sanitize_ip() {
  local ip="$1"
  echo "$ip" | tr -d '\r\n' | sed 's/[^0-9\.]//g'
}

if [[ -f "$SERVER_LIST_FILE" ]]; then
  # 从文件读取IP列表，忽略注释和空行
  raw_ip_list=($(grep -v '^\s*#\|^\s*$' "$SERVER_LIST_FILE"))

  # 清理和验证IP地址
  IPLIST=()
  invalid_ips=()
  for ip in "${raw_ip_list[@]}"; do
    clean_ip=$(sanitize_ip "$ip")
    # 简单验证IP格式
    if [[ $clean_ip =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
      IPLIST+=("$clean_ip")
    else
      invalid_ips+=("$ip")
    fi
  done

  log "从文件加载了 ${#IPLIST[@]} 个有效服务器IP"

  # 如果有无效IP，记录到日志
  if [[ ${#invalid_ips[@]} -gt 0 ]]; then
    log "警告: 发现 ${#invalid_ips[@]} 个无效IP地址"
    for invalid_ip in "${invalid_ips[@]}"; do
      log "无效IP: $invalid_ip"
    done
  fi

  # 如果没有有效IP，提示手动输入
  if [[ ${#IPLIST[@]} -eq 0 ]]; then
    log "文件中没有有效IP，请手动输入IP地址，使用空格隔开"
    read -r raw_input
    raw_ip_list=($raw_input)

    # 清理和验证手动输入的IP
    IPLIST=()
    for ip in "${raw_ip_list[@]}"; do
      clean_ip=$(sanitize_ip "$ip")
      if [[ $clean_ip =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
        IPLIST+=("$clean_ip")
      fi
    done

    log "已添加 ${#IPLIST[@]} 个有效服务器IP"
  fi
else
  log "未找到服务器列表文件 $SERVER_LIST_FILE"
  echo "请手动输入服务器IP地址，使用空格隔开："
  read -r raw_input
  raw_ip_list=($raw_input)

  # 清理和验证手动输入的IP
  IPLIST=()
  for ip in "${raw_ip_list[@]}"; do
    clean_ip=$(sanitize_ip "$ip")
    if [[ $clean_ip =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
      IPLIST+=("$clean_ip")
    fi
  done

  log "已添加 ${#IPLIST[@]} 个有效服务器IP"
fi

# 检查重复的IP地址
IPLIST=($(echo "${IPLIST[@]}" | tr ' ' '\n' | sort -u | tr '\n' ' '))
log "优化后的服务器IP数量: ${#IPLIST[@]}"

# 设置SFTP账号和密码
USER="ftpuser"
PASSWORDLIST=("Mk@83cka11" "Mk@83cka2311")

# 端口
PORT=22

# 连接超时时间(秒)
TIMEOUT=30

# 成功标记文件
SUCCESS_FILE="$SRCDIR/download_success.txt"
touch "$SUCCESS_FILE"

# 检查路径和文件存在性的函数
check_path_and_files() {
  local ip=$1
  local password=$2
  local desdir=$3
  local target_path=$4

  log "检查路径可达性: $ip:$desdir/$target_path"

  # 使用lftp检查路径是否存在以及是否有文件
  local check_result=$(timeout $TIMEOUT lftp -u "$USER,$password" "sftp://${ip}:${PORT}" 2>/dev/null <<EOF
set sftp:connect-program "ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null"
set net:timeout $TIMEOUT
cd "$desdir" || exit 1
ls "$target_path" 2>/dev/null || exit 1
quit
EOF
)

  local exit_code=$?

  if [[ $exit_code -eq 0 ]] && [[ -n "$check_result" ]]; then
    local file_count=$(echo "$check_result" | wc -l)
    log "路径检查成功: $ip:$desdir/$target_path (发现 $file_count 个文件/目录)"
    return 0
  else
    log "路径检查失败: $ip:$desdir/$target_path (路径不存在或为空)"
    return 1
  fi
}

# 下载函数，减少重复代码
download_data() {
  local ip=$1
  local password=$2
  local desdir=$3
  local nodeb_dir=$4
  local target_path="$current_date"

  if [[ -n "$nodeb_dir" ]]; then
    target_path="$current_date/$nodeb_dir"
  fi

  # 检查是否已经成功下载过
  if grep -q "${ip}_${target_path}" "$SUCCESS_FILE"; then
    log "跳过已成功下载: $ip - $target_path"
    return 0
  fi

  # 先检查路径和文件是否存在
  if ! check_path_and_files "$ip" "$password" "$desdir" "$target_path"; then
    log "跳过下载: $ip - $target_path (路径不可达或无文件)"
    return 1
  fi

  log "开始下载: $ip - $target_path"

  # 创建本地目录
  mkdir -p "$SRCDIR/$target_path"

  # 设置超时
  timeout $TIMEOUT lftp -u "$USER,$password" "sftp://${ip}:${PORT}" 2>/dev/null <<EOF || return 1
  set sftp:connect-program "ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null"
  set net:timeout $TIMEOUT
  set net:max-retries 2
  set net:reconnect-interval-base 2
  cd "$desdir"
  lcd "$SRCDIR"
  mirror --only-newer --parallel=5 "$target_path/" "$SRCDIR/$target_path"
  close
  quit
EOF

  # 检查下载结果
  if [[ $? -eq 0 ]]; then
    log "下载成功: $ip - $target_path"
    echo "${ip}_${target_path}" >> "$SUCCESS_FILE"
    return 0
  else
    log "下载失败: $ip - $target_path"
    return 1
  fi
}

# 主下载逻辑
download_count=0
success_count=0
failure_count=0

# 对每个服务器尝试连接
for IP in "${IPLIST[@]}"; do
  ip_connected=false
  successful_password=""

  # 尝试每个密码
  for PASSWORD in "${PASSWORDLIST[@]}"; do
    if $ip_connected; then
      break  # 已经连接成功，跳过其他密码
    fi

    log "尝试 IP:$IP 密码:${PASSWORD:0:3}*** 用户:$USER"

    # 尝试每个目标目录
    for DESDIR in "${DESDIRLIST[@]}"; do
      if $ip_connected; then
        break  # 已经连接成功，跳过其他目录
      fi

      log "测试目录:$DESDIR"

      # 先测试基本连接
      if timeout $TIMEOUT lftp -u "$USER,$PASSWORD" "sftp://${IP}:${PORT}" -e "exit" 2>/dev/null; then
        log "基本连接成功: $IP (密码: ${PASSWORD:0:3}***)"

        # 检查目标路径是否可达且有文件
        local path_valid=false

        # 根据Nodeb_set是否为空决定检查方式
        if [[ ${#Nodeb_set[@]} -eq 0 ]]; then
          # 检查整个日期目录
          if check_path_and_files "$IP" "$PASSWORD" "$DESDIR" "$current_date"; then
            path_valid=true
            log "路径验证成功: $IP:$DESDIR/$current_date"
          fi
        else
          # 检查至少一个基站目录是否存在且有文件
          for NODEB_DIR in "${Nodeb_set[@]}"; do
            if check_path_and_files "$IP" "$PASSWORD" "$DESDIR" "$current_date/$NODEB_DIR"; then
              path_valid=true
              log "基站路径验证成功: $IP:$DESDIR/$current_date/$NODEB_DIR"
              break  # 找到一个有效路径就足够了
            fi
          done
        fi

        if $path_valid; then
          ip_connected=true
          successful_password="$PASSWORD"
          log "服务器验证完成: $IP (连接+路径+文件检查通过)"

          # 执行实际下载
          if [[ ${#Nodeb_set[@]} -eq 0 ]]; then
            log "下载整个日期目录: $current_date"
            download_data "$IP" "$successful_password" "$DESDIR" ""

            if [[ $? -eq 0 ]]; then
              success_count=$((success_count + 1))
            else
              failure_count=$((failure_count + 1))
            fi
            download_count=$((download_count + 1))
          else
            # 只下载指定的基站
            for NODEB_DIR in "${Nodeb_set[@]}"; do
              log "下载基站: $NODEB_DIR"
              download_data "$IP" "$successful_password" "$DESDIR" "$NODEB_DIR"

              if [[ $? -eq 0 ]]; then
                success_count=$((success_count + 1))
              else
                failure_count=$((failure_count + 1))
              fi
              download_count=$((download_count + 1))
            done
          fi
          break  # 成功验证后跳出目录循环
        else
          log "路径验证失败: $IP:$DESDIR (路径不存在或无文件)"
        fi
      else
        log "连接失败: $IP (密码: ${PASSWORD:0:3}***)"
      fi
    done
  done

  if ! $ip_connected; then
    log "警告: 无法连接到服务器 $IP (尝试了 ${#PASSWORDLIST[@]} 个密码)"
  fi
done

log "下载统计: 总共 $download_count, 成功 $success_count, 失败 $failure_count"

# 如果没有成功的下载，则退出
if [[ $success_count -eq 0 ]]; then
  log "错误: 没有成功的下载，退出"
  exit 1
fi

# 压缩下载的文件
ARCHIVE_NAME="5G基站PCIMR-${current_date}.tar.gz"
log "开始压缩文件: $ARCHIVE_NAME"
tar -czf "$ARCHIVE_NAME" -C "$(dirname "$SRCDIR")" "$(basename "$SRCDIR")"

if [[ $? -eq 0 ]]; then
  log "压缩成功: $ARCHIVE_NAME"

  # 计算MD5校验和
  md5sum "$ARCHIVE_NAME" > "${ARCHIVE_NAME}.md5"
  log "生成MD5校验和: ${ARCHIVE_NAME}.md5"

  # 直接保留原始文件，不进行确认
  log "保留原始文件"
else
  log "压缩失败"
fi

log "脚本执行完成"