#!/bin/bash
# 后台运行下载脚本的包装器

# 检查参数
if [[ $# -lt 2 ]]; then
  echo "用法: $0 <日期(YYYYMMDD)> <任务名称> [基站列表(可选)]"
  echo "示例: $0 20250418 beijing_task \"成都北HR04H 武侯HR03H 锦江HR03H\""
  exit 1
fi

# 获取参数
DATE=$1
TASK_NAME=$2
NODEB_LIST="${3:-}"  # 如果没有提供基站列表，则为空

# 创建临时输入文件
INPUT_FILE=$(mktemp)

# 写入日期和确认
echo "$DATE" > "$INPUT_FILE"
echo "y" >> "$INPUT_FILE"
echo "$TASK_NAME" >> "$INPUT_FILE"

# 如果提供了基站列表，则写入
if [[ -n "$NODEB_LIST" ]]; then
  echo "$NODEB_LIST" >> "$INPUT_FILE"
fi

# 创建日志目录
LOG_DIR="logs"
mkdir -p "$LOG_DIR"

# 生成唯一的日志文件名
LOG_FILE="$LOG_DIR/download_${TASK_NAME}_${DATE}_$(date +%Y%m%d_%H%M%S).log"

# 使用nohup在后台运行脚本，将输入重定向到临时文件
echo "开始在后台运行下载任务..."
echo "日期: $DATE"
echo "任务名称: $TASK_NAME"
if [[ -n "$NODEB_LIST" ]]; then
  echo "基站列表: $NODEB_LIST"
fi
echo "日志文件: $LOG_FILE"

# 使用nohup在后台运行脚本
nohup bash -c "cat $INPUT_FILE | ./one_new_script\ copy.sh > $LOG_FILE 2>&1 && rm $INPUT_FILE" &

# 获取进程ID
PID=$!
echo "后台进程ID: $PID"
echo "可以使用 'tail -f $LOG_FILE' 命令查看下载进度"
echo "可以使用 'ps -p $PID' 命令检查进程是否仍在运行"
