#!/bin/bash
# 使用screen在虚拟终端中运行下载脚本的包装器

# 检查screen是否已安装
if ! command -v screen &> /dev/null; then
  echo "错误: screen命令未安装。请先安装screen:"
  echo "  Debian/Ubuntu: sudo apt-get install screen"
  echo "  CentOS/RHEL: sudo yum install screen"
  exit 1
fi

# 检查参数
if [[ $# -lt 2 ]]; then
  echo "用法: $0 <日期(YYYYMMDD)> <任务名称> [基站列表(可选)]"
  echo "示例: $0 20250418 beijing_task \"成都北HR04H 武侯HR03H 锦江HR03H\""
  exit 1
fi

# 获取参数
DATE=$1
TASK_NAME=$2
NODEB_LIST="${3:-}"  # 如果没有提供基站列表，则为空

# 创建临时输入文件
INPUT_FILE=$(mktemp)

# 写入日期和确认
echo "$DATE" > "$INPUT_FILE"
echo "y" >> "$INPUT_FILE"
echo "$TASK_NAME" >> "$INPUT_FILE"

# 如果提供了基站列表，则写入
if [[ -n "$NODEB_LIST" ]]; then
  echo "$NODEB_LIST" >> "$INPUT_FILE"
fi

# 创建日志目录
LOG_DIR="logs"
mkdir -p "$LOG_DIR"

# 生成唯一的日志文件名
LOG_FILE="$LOG_DIR/download_${TASK_NAME}_${DATE}_$(date +%Y%m%d_%H%M%S).log"

# 创建screen会话名称
SCREEN_NAME="download_${TASK_NAME}_${DATE}"

# 显示信息
echo "开始在screen会话中运行下载任务..."
echo "日期: $DATE"
echo "任务名称: $TASK_NAME"
if [[ -n "$NODEB_LIST" ]]; then
  echo "基站列表: $NODEB_LIST"
fi
echo "screen会话名称: $SCREEN_NAME"
echo "日志文件: $LOG_FILE"

# 创建screen会话并在其中运行脚本
screen -dmS "$SCREEN_NAME" bash -c "cat $INPUT_FILE | ./one_new_script\ copy.sh > $LOG_FILE 2>&1; rm $INPUT_FILE; echo '按任意键退出'; read -n 1"

# 显示如何重新连接到screen会话的说明
echo ""
echo "下载任务已在screen会话中启动"
echo "可以使用以下命令重新连接到screen会话:"
echo "  screen -r $SCREEN_NAME"
echo ""
echo "在screen会话中，可以使用以下快捷键:"
echo "  Ctrl+A 然后 D: 分离会话并返回到原始终端"
echo "  Ctrl+A 然后 K: 终止会话"
echo ""
echo "可以使用 'tail -f $LOG_FILE' 命令查看下载进度"
