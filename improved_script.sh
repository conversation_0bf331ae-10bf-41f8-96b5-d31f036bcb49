#!/bin/bash
# 获取基站数据下载脚本
# 描述: 通过SFTP从多台服务器下载5G基站的MR数据

# 严格模式设置
set -euo pipefail

# 多线程设置
MAX_PARALLEL_DOWNLOADS=15  # 增加最大并行下载数
MAX_PARALLEL_FILES=8      # 每个LFTP会话的并行文件数
# 日期和任务名称设置

# 提示用户输入日期
echo "请输入需要下载的数据日期(格式:YYYYMMDD,例如:20250418):"
read -r input_date

# 验证日期格式
while ! [[ $input_date =~ ^[0-9]{8}$ ]]; do
  echo "日期格式不正确,请重新输入(格式:YYYYMMDD):"
  read -r input_date
done

# 确认日期
current_date=$input_date
echo "您输入的日期是：$current_date"
echo "这个日期是否正确？ (y/n)"
read -r confirm

while [[ ! $confirm =~ ^[Yy]$ ]]; do
  echo "请重新输入日期(格式:YYYYMMDD):"
  read -r input_date

  # 验证日期格式
  while ! [[ $input_date =~ ^[0-9]{8}$ ]]; do
    echo "日期格式不正确,请重新输入(格式:YYYYMMDD):"
    read -r input_date
  done

  current_date=$input_date
  echo "您输入的日期是：$current_date"
  echo "这个日期是否正确？ (y/n)"
  read -r confirm
done

# 提示用户输入任务名称
echo "请输入任务名称（仅允许字母、数字和下划线，不允许空格和特殊字符）："
read -r task_name

# 验证任务名称
while ! [[ $task_name =~ ^[a-zA-Z0-9_]+$ ]]; do
  echo "任务名称格式不正确，只允许字母、数字和下划线，请重新输入："
  read -r task_name
done

# 下载文件名设置
# 使用英文命名避免编码问题
ARCHIVE_NAME="${task_name}_5G_Station_PCIMR-${current_date}.tar.gz"

# 日志文件
LOG_DIR="./logs"
mkdir -p "$LOG_DIR"
LOG_FILE="$LOG_DIR/download_log_$(date +%Y%m%d_%H%M%S).txt"

# 临时目录存储统计信息，使用PID确保唯一性
STATS_DIR="/tmp/sftp_stats_$$"
mkdir -p "$STATS_DIR"

# 初始化日志
echo "[$(date '+%Y-%m-%d %H:%M:%S')] 脚本开始执行" > "$LOG_FILE"

# 进度显示
show_progress() {
  local completed=$1
  local total=$2
  local percent=$((completed * 100 / total))
  local bar_size=40
  local bar_filled=$((percent * bar_size / 100))
  local bar_empty=$((bar_size - bar_filled))

  # 创建进度条
  local bar=""
  for ((i=0; i<bar_filled; i++)); do
    bar+="#"
  done
  for ((i=0; i<bar_empty; i++)); do
    bar+=" "
  done

  # 显示进度条和百分比
  echo -ne "下载进度: [${bar}] ${percent}% (${completed}/${total})\r"
}

# 错误处理函数
handle_error() {
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] 错误发生在第 $1 行: $2" >> "$LOG_FILE"
  exit 1
}

# 设置错误处理
trap 'handle_error ${LINENO} "$BASH_COMMAND"' ERR

# 清理临时文件
cleanup() {
  rm -rf "$STATS_DIR"
}
trap cleanup EXIT

# 清理站点名称的函数
sanitize_station_name() {
  local station_name="$1"

  echo "$station_name" | tr -d '\r\n' | sed 's/[^[:alnum:][:space:]一-龥]/_/g'
}

# 清理IP地址的函数
sanitize_ip() {
  local ip="$1"

  echo "$ip" | tr -d '\r\n' | sed 's/[^0-9\.]//g'
}

# 定义检查空文件夹的函数
check_empty_dirs() {
  local dir="$1"
  local date_dir="$2"
  local empty_dirs=0
  local total_dirs=0
  local empty_dir_list="$STATS_DIR/empty_dirs.txt"

  # 初始化空文件夹列表
  : > "$empty_dir_list"

  # 检查站点文件夹是否为空
  for nodeb in "${Nodeb_set[@]}"; do
    local station_dir="$dir/$date_dir/$nodeb"

    # 如果站点目录存在，检查是否为空
    if [[ -d "$station_dir" ]]; then
      total_dirs=$((total_dirs + 1))

      # 检查目录是否为空
      if [[ -z "$(ls -A "$station_dir" 2>/dev/null)" ]]; then
        echo "$station_dir (站点: $nodeb)" >> "$empty_dir_list"
        empty_dirs=$((empty_dirs + 1))
      fi
    fi
  done

  # 返回空文件夹数量和总文件夹数量
  echo "$empty_dirs $total_dirs"
}

# 定义检查运行中作业的函数
check_running_jobs() {
  for job_to_check in $(cat "$TASK_TRACKER" 2>/dev/null); do
    if [[ -f "$STATS_DIR/done_${job_to_check}" ]]; then
      sed -i "/^${job_to_check}$/d" "$TASK_TRACKER" 2>/dev/null || true
      running_jobs=$((running_jobs - 1))
      completed_jobs=$((completed_jobs + 1))
      show_progress $completed_jobs $total_tasks
    fi
  done
  sleep 0.5
}

# 设置SFTP账号和密码
USER="ftpuser"
PASSWORDLIST=("Mk@83cka" "Mk@83cka23")

# 目标目录(本地目录)
SRCDIR="/home/<USER>/MR_get/${task_name}_5G_Station_PCIMR-$current_date"
mkdir -p "$SRCDIR"

# FTP目录(服务器文件目录)
DESDIRLIST=("/export/home/<USER>/internalftp/var/SauService/filter/TSInventory/gnodeb_sig")

# 使用变量前进行验证，确保不为空
if [[ -z "$task_name" || -z "$current_date" ]]; then
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] 错误: 任务名称或日期为空" | tee -a "$LOG_FILE"
  exit 1
fi
echo "[$(date '+%Y-%m-%d %H:%M:%S')] 开始下载任务: ${task_name}, 日期: $current_date 的数据" | tee -a "$LOG_FILE"

# 检查是否有节点列表文件
NODEB_LIST_FILE="nodeb_list.txt"
if [[ -f "$NODEB_LIST_FILE" ]]; then
  raw_nodeb_set=($(grep -v '^\s*#\|^\s*$' "$NODEB_LIST_FILE"))
  Nodeb_set=()
  for nodeb in "${raw_nodeb_set[@]}"; do
    clean_nodeb=$(sanitize_station_name "$nodeb")
    Nodeb_set+=("$clean_nodeb")
  done
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] 从文件加载了 ${#Nodeb_set[@]} 个基站节点" >> "$LOG_FILE"
else
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] 未找到节点列表文件，请手动添加站点列表使用空格隔开" >> "$LOG_FILE"
  read -r -a Nodeb_set
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] 已添加 ${#Nodeb_set[@]} 个基站节点" >> "$LOG_FILE"
fi

# SFTP服务器IP列表文件
SERVER_LIST_FILE="server_list.txt"
echo "[$(date '+%Y-%m-%d %H:%M:%S')] 尝试从 $SERVER_LIST_FILE 文件加载服务器IP列表" | tee -a "$LOG_FILE"

if [[ -f "$SERVER_LIST_FILE" ]]; then
  # 从文件读取IP列表，忽略注释和空行
  raw_ip_list=($(grep -v '^\s*#\|^\s*$' "$SERVER_LIST_FILE"))

  # 清理和验证IP地址
  IPLIST=()
  invalid_ips=()
  for ip in "${raw_ip_list[@]}"; do
    clean_ip=$(sanitize_ip "$ip")
    # 简单验证IP格式
    if [[ $clean_ip =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
      IPLIST+=("$clean_ip")
    else
      invalid_ips+=("$ip")
    fi
  done

  echo "[$(date '+%Y-%m-%d %H:%M:%S')] 从文件加载了 ${#IPLIST[@]} 个有效服务器IP" | tee -a "$LOG_FILE"

  # 如果有无效IP，记录到日志
  if [[ ${#invalid_ips[@]} -gt 0 ]]; then
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 警告: 发现 ${#invalid_ips[@]} 个无效IP地址" | tee -a "$LOG_FILE"
    for invalid_ip in "${invalid_ips[@]}"; do
      echo "[$(date '+%Y-%m-%d %H:%M:%S')] 无效IP: $invalid_ip" >> "$LOG_FILE"
    done
  fi

  # 如果没有有效IP，提示手动输入
  if [[ ${#IPLIST[@]} -eq 0 ]]; then
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 文件中没有有效IP，请手动输入IP地址，使用空格隔开" | tee -a "$LOG_FILE"
    read -r raw_input
    raw_ip_list=($raw_input)

    # 清理和验证手动输入的IP
    IPLIST=()
    for ip in "${raw_ip_list[@]}"; do
      clean_ip=$(sanitize_ip "$ip")
      if [[ $clean_ip =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
        IPLIST+=("$clean_ip")
      fi
    done

    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 已添加 ${#IPLIST[@]} 个有效服务器IP" | tee -a "$LOG_FILE"
  fi
else
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] 未找到服务器列表文件 $SERVER_LIST_FILE" | tee -a "$LOG_FILE"
  echo "请手动输入服务器IP地址，使用空格隔开："
  read -r raw_input
  raw_ip_list=($raw_input)

  # 清理和验证手动输入的IP
  IPLIST=()
  for ip in "${raw_ip_list[@]}"; do
    clean_ip=$(sanitize_ip "$ip")
    if [[ $clean_ip =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
      IPLIST+=("$clean_ip")
    fi
  done

  echo "[$(date '+%Y-%m-%d %H:%M:%S')] 已添加 ${#IPLIST[@]} 个有效服务器IP" | tee -a "$LOG_FILE"
fi

# 检查重复的IP地址
IPLIST=($(echo "${IPLIST[@]}" | tr ' ' '\n' | sort -u | tr '\n' ' '))
echo "[$(date '+%Y-%m-%d %H:%M:%S')] 优化后的服务器IP数量: ${#IPLIST[@]}" >> "$LOG_FILE"

# 端口
PORT=22

# 连接超时时间(秒)
TIMEOUT=45       # 增加超时时间
CONN_RETRIES=3   # 连接重试次数
TRANSFER_RATE_LIMIT="0"  # 传输速率限制，0表示不限制

# 成功标记文件
SUCCESS_FILE="$SRCDIR/download_success.txt"
touch "$SUCCESS_FILE"

# 下载函数，减少重复代码
download_data() {
  local job_id=$1
  local ip=$2
  local password=$3
  local desdir=$4
  local nodeb_dir=$5
  local result_file="$STATS_DIR/result_${job_id}"

  # 初始化结果文件为空
  : > "$result_file"

  # 设置目标路径，确保远程和本地路径一致
  local target_path
  if [[ -n "$nodeb_dir" ]]; then
    # 如果有站点名称，路径为日期/站点名称
    target_path="$current_date/$nodeb_dir"
  else
    # 如果没有站点名称，路径仅为日期
    target_path="$current_date"
  fi

  # 打印调试信息，显示路径设置
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] 设置目标路径: $target_path" >> "$LOG_FILE"

  # 添加调试信息
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] 尝试从 ${ip}:${desdir}/${target_path} 下载到 ${SRCDIR}/${target_path}" >> "$LOG_FILE"

  # 计算下载开始时间
  start_time=$(date +%s)

  # 检查是否已经成功下载过
  if grep -q "${ip}_${target_path}" "$SUCCESS_FILE" 2>/dev/null; then
    echo "SKIP:$job_id:$ip:$target_path" > "$result_file"
    return 0
  fi

  # 创建本地目录，确保与远程路径一致
  local local_dir="$SRCDIR/$target_path"
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] 创建本地目录: $local_dir" >> "$LOG_FILE"
  mkdir -p "$local_dir"

  # 测试连接
  if ! timeout $((TIMEOUT / 2)) lftp -u "$USER,$password" "sftp://${ip}:${PORT}" -e "exit" 2>/dev/null; then
    echo "CONN_FAIL:$job_id:$ip" > "$result_file"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 连接失败: ${ip}" >> "$LOG_FILE"
    return 1
  fi

  # 检查远程目录是否存在
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] 检查远程目录: ${desdir}/${target_path}" >> "$LOG_FILE"
  if timeout $((TIMEOUT / 2)) lftp -u "$USER,$password" "sftp://${ip}:${PORT}" -e "ls -la ${desdir}/${target_path}; exit" >> "$LOG_FILE" 2>&1; then
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 远程目录存在" >> "$LOG_FILE"
  else
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 警告: 远程目录可能不存在" >> "$LOG_FILE"
  fi

  # 设置超时并进行下载
  if timeout $TIMEOUT lftp -u "$USER,$password" "sftp://${ip}:${PORT}" 2>/dev/null <<EOF; then
  # 优化LFTP连接参数
  set sftp:connect-program "ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null"
  set net:timeout $TIMEOUT
  set net:max-retries 2
  set net:reconnect-interval-base 2
  # 设置远程目录
  cd "$desdir"
  # 设置本地目录
  lcd "$SRCDIR"
  # 打印调试信息，显示当前目录设置
  !echo "[$(date '+%Y-%m-%d %H:%M:%S')] LFTP远程目录: $desdir, 本地目录: $SRCDIR" >> "$LOG_FILE"
  # 使用更高效的下载参数
  # 确保路径一致性，注意源路径添加斜杠
  !echo "[$(date '+%Y-%m-%d %H:%M:%S')] 执行mirror命令: '$target_path/' -> '$target_path'" >> "$LOG_FILE"
  # 将输出重定向到日志文件
  mirror --continue --only-newer --parallel=$MAX_PARALLEL_FILES --verbose=1 "$target_path/" "$target_path" >> "$LOG_FILE" 2>&1
  close
  quit
EOF
    # 计算下载结束时间和耗时
    end_time=$(date +%s)
    duration=$((end_time - start_time))

    # 检查是否有文件被下载
    if [[ -z "$(find "$SRCDIR/$target_path" -type f 2>/dev/null)" ]]; then
      echo "[$(date '+%Y-%m-%d %H:%M:%S')] 警告: 目录 $SRCDIR/$target_path 下载后为空" | tee -a "$LOG_FILE"
      echo "EMPTY:$job_id:$ip:$target_path" > "$result_file"
      return 1
    fi

    # 记录成功
    echo "${ip}_${target_path}" >> "$SUCCESS_FILE"
    echo "SUCCESS:$job_id:$ip:$target_path" > "$result_file"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 下载成功: ${ip}:${desdir}/${target_path} (耗时: ${duration}秒)" >> "$LOG_FILE"

    # 检查下载的文件数量和大小
    file_count=$(find "$SRCDIR/$target_path" -type f | wc -l)
    total_size=$(du -sh "$SRCDIR/$target_path" | cut -f1)
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 下载统计: 文件数量=${file_count}, 总大小=${total_size}" >> "$LOG_FILE"

    return 0
  else
    # 记录失败
    echo "FAIL:$job_id:$ip:$target_path" > "$result_file"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 下载失败: ${ip}:${desdir}/${target_path}" >> "$LOG_FILE"
    return 1
  fi
}

# 维护有效连接的服务器列表
valid_servers=()

# 测试服务器连接有效性
echo "正在测试服务器连接..."

# 并行测试服务器连接
test_connection() {
  local ip=$1
  local user=$2
  local password=$3
  local port=$4
  local result_file="$STATS_DIR/conn_${ip//./}_${port}"

  if timeout 5 lftp -u "$user,$password" "sftp://${ip}:${port}" -e "exit" 2>/dev/null; then
    echo "SUCCESS:$ip:$password" > "$result_file"
  else
    echo "FAIL:$ip:$password" > "$result_file"
  fi
}

# 启动并行连接测试
for IP in "${IPLIST[@]}"; do
  for PASSWORD in "${PASSWORDLIST[@]}"; do
    test_connection "$IP" "$USER" "$PASSWORD" "$PORT" &
  done
done

# 等待所有连接测试完成
wait

# 处理测试结果
for IP in "${IPLIST[@]}"; do
  for PASSWORD in "${PASSWORDLIST[@]}"; do
    result_file="$STATS_DIR/conn_${IP//./}_${PORT}"
    if [[ -f "$result_file" ]] && grep -q "^SUCCESS:" "$result_file"; then
      valid_servers+=("$IP:$PASSWORD")
      echo "[$(date '+%Y-%m-%d %H:%M:%S')] 服务器连接成功: $IP" >> "$LOG_FILE"
      break  # 找到有效密码就跳出
    fi
  done
done

if [[ ${#valid_servers[@]} -eq 0 ]]; then
  echo "错误: 没有任何可访问的服务器。请检查网络或凭证。" | tee -a "$LOG_FILE"
  exit 1
fi

echo "有效服务器数量: ${#valid_servers[@]}"

# 计算总任务数
total_tasks=0
for server_info in "${valid_servers[@]}"; do
  IFS=':' read -r IP PASSWORD <<< "$server_info"
  for DESDIR in "${DESDIRLIST[@]}"; do
    if [[ ${#Nodeb_set[@]} -eq 0 ]]; then
      total_tasks=$((total_tasks + 1))
    else
      total_tasks=$((total_tasks + ${#Nodeb_set[@]}))
    fi
  done
done

if [[ $total_tasks -eq 0 ]]; then
  echo "错误: 没有可执行的下载任务。" | tee -a "$LOG_FILE"
  exit 1
fi

echo "总计划下载任务数: $total_tasks"

# 主下载逻辑
job_id=0
running_jobs=0
completed_jobs=0

# 创建任务跟踪文件
TASK_TRACKER="$STATS_DIR/task_tracker"
: > "$TASK_TRACKER"

# 对每个有效服务器进行下载
for server_info in "${valid_servers[@]}"; do
  IFS=':' read -r IP PASSWORD <<< "$server_info"

  for DESDIR in "${DESDIRLIST[@]}"; do
    # 根据Nodeb_set是否为空决定下载方式
    if [[ ${#Nodeb_set[@]} -eq 0 ]]; then
      # 等待可用线程
      while [[ $running_jobs -ge $MAX_PARALLEL_DOWNLOADS ]]; do
        check_running_jobs
      done

      # 启动下载任务
      job_id=$((job_id + 1))
      echo "$job_id" >> "$TASK_TRACKER"
      (download_data "$job_id" "$IP" "$PASSWORD" "$DESDIR" ""; touch "$STATS_DIR/done_${job_id}") &
      running_jobs=$((running_jobs + 1))
    else
      # 只下载指定的基站
      for NODEB_DIR in "${Nodeb_set[@]}"; do
        # 等待可用线程
        while [[ $running_jobs -ge $MAX_PARALLEL_DOWNLOADS ]]; do
          check_running_jobs
        done

        # 启动下载任务
        job_id=$((job_id + 1))
        echo "$job_id" >> "$TASK_TRACKER"
        (download_data "$job_id" "$IP" "$PASSWORD" "$DESDIR" "$NODEB_DIR"; touch "$STATS_DIR/done_${job_id}") &
        running_jobs=$((running_jobs + 1))
      done
    fi
  done
done

# 等待所有任务完成
echo "等待所有下载任务完成..."
while [[ $completed_jobs -lt $total_tasks ]]; do
  check_running_jobs

  # 如果没有正在运行的任务但任务未完成，可能是因为有任务未被捕获
  if [[ $running_jobs -eq 0 && $completed_jobs -lt $total_tasks ]]; then
    echo "警告：所有任务已结束，但统计数量不匹配" | tee -a "$LOG_FILE"
    completed_jobs=$total_tasks  # 强制退出循环
  fi
done

# 等待所有后台进程完成
wait

echo -e "\n所有下载任务已完成 (${completed_jobs}/${total_tasks})"

# 统计结果
if ls "$STATS_DIR"/result_* &>/dev/null; then
  success_count=$(grep -c "^SUCCESS:" "$STATS_DIR"/result_* 2>/dev/null || echo 0)
  fail_count=$(grep -c "^FAIL:" "$STATS_DIR"/result_* 2>/dev/null || echo 0)
  skip_count=$(grep -c "^SKIP:" "$STATS_DIR"/result_* 2>/dev/null || echo 0)
  conn_fail_count=$(grep -c "^CONN_FAIL:" "$STATS_DIR"/result_* 2>/dev/null || echo 0)
  empty_count=$(grep -c "^EMPTY:" "$STATS_DIR"/result_* 2>/dev/null || echo 0)
else
  success_count=0
  fail_count=0
  skip_count=0
  conn_fail_count=0
  empty_count=0
fi

# 计算总体下载速度
# 获取脚本总运行时间
script_end_time=$(date +%s)
# 使用更兼容的方式获取脚本开始时间
script_start_time=$(head -n 1 "$LOG_FILE" | sed -E 's/.*\[([0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2})\].*/\1/')
script_start_seconds=$(date -j -f "%Y-%m-%d %H:%M:%S" "$script_start_time" "+%s" 2>/dev/null || date -d "$script_start_time" "+%s" 2>/dev/null || echo "$script_end_time")
script_duration=$((script_end_time - script_start_seconds))
# 确保脚本时间至少为1秒
if [[ $script_duration -le 0 ]]; then
  script_duration=1
fi

# 获取总下载文件大小
total_downloaded_size=$(du -s "$SRCDIR" 2>/dev/null | cut -f1 || echo 0)

# 计算平均下载速度 (KB/s)
if [[ $script_duration -gt 0 && $total_downloaded_size -gt 0 ]]; then
  avg_speed=$((total_downloaded_size / script_duration))
else
  avg_speed=0
fi

echo "下载统计:"
echo "  成功: $success_count"
echo "  失败: $fail_count"
echo "  连接失败: $conn_fail_count"
echo "  空目录: $empty_count"
echo "  跳过: $skip_count"
echo "  总计: $((success_count + fail_count + skip_count + conn_fail_count + empty_count))"
echo "  总运行时间: ${script_duration}秒"
echo "  总下载大小: $(du -sh "$SRCDIR" 2>/dev/null | cut -f1)"
echo "  平均下载速度: ${avg_speed} KB/s"
# 空文件夹统计将在检查后显示

# 初始化空文件夹统计变量
empty_dirs_count=0
total_dirs_count=0

# 将下载统计信息添加到日志
echo "[$(date '+%Y-%m-%d %H:%M:%S')] 下载统计: 成功=$success_count, 失败=$fail_count, 连接失败=$conn_fail_count, 空目录=$empty_count, 跳过=$skip_count, 速度=${avg_speed}KB/s" >> "$LOG_FILE"

# 如果没有成功的下载且不是所有都跳过，则退出
if [[ $success_count -eq 0 && $skip_count -ne $total_tasks ]]; then
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] 错误: 没有成功的下载，退出" | tee -a "$LOG_FILE"
  exit 1
fi

# 在压缩前检查空文件夹
echo "[$(date '+%Y-%m-%d %H:%M:%S')] 开始检查空文件夹..." | tee -a "$LOG_FILE"

# 执行空文件夹检查
if [[ -d "$SRCDIR" && ${#Nodeb_set[@]} -gt 0 ]]; then
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] 检查站点文件夹是否为空..." | tee -a "$LOG_FILE"
  # 打印调试信息
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] 源目录: $SRCDIR, 日期目录: $current_date, 站点数量: ${#Nodeb_set[@]}" >> "$LOG_FILE"
  empty_dirs_result=$(check_empty_dirs "$SRCDIR" "$current_date")
  empty_dirs_count=$(echo "$empty_dirs_result" | cut -d' ' -f1)
  total_dirs_count=$(echo "$empty_dirs_result" | cut -d' ' -f2)

  # 将空站点文件夹信息添加到统计中
  echo "  空站点文件夹: $empty_dirs_count/$total_dirs_count"

  # 将空站点文件夹信息添加到日志
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] 空站点文件夹检查结果: $empty_dirs_count/$total_dirs_count" >> "$LOG_FILE"

  # 如果有空站点文件夹，列出前10个
  if [[ $empty_dirs_count -gt 0 ]]; then
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 空站点文件夹示例(前10个):" >> "$LOG_FILE"
    head -n 10 "$STATS_DIR/empty_dirs.txt" >> "$LOG_FILE"
    if [[ $empty_dirs_count -gt 10 ]]; then
      echo "[$(date '+%Y-%m-%d %H:%M:%S')] ... 及其他 $((empty_dirs_count - 10)) 个空站点文件夹" >> "$LOG_FILE"
    fi
  fi
elif [[ ! -d "$SRCDIR" ]]; then
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] 警告: 无法检查空站点文件夹，目录 $SRCDIR 不存在" | tee -a "$LOG_FILE"
else
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] 警告: 无法检查空站点文件夹，没有站点列表" | tee -a "$LOG_FILE"
fi

# 将空站点文件夹信息添加到日志汇总
echo "[$(date '+%Y-%m-%d %H:%M:%S')] 下载统计汇总: 成功=$success_count, 失败=$fail_count, 连接失败=$conn_fail_count, 空目录=$empty_count, 跳过=$skip_count, 空站点文件夹=$empty_dirs_count/$total_dirs_count, 速度=${avg_speed}KB/s" >> "$LOG_FILE"

# 压缩下载的文件
# 再次验证变量和路径
if [[ -z "$ARCHIVE_NAME" || -z "$SRCDIR" ]]; then
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] 错误: 压缩文件名或源目录为空" | tee -a "$LOG_FILE"
  exit 1
fi
echo "[$(date '+%Y-%m-%d %H:%M:%S')] 开始压缩文件: $ARCHIVE_NAME" | tee -a "$LOG_FILE"
echo "任务名称: ${task_name}, 日期: ${current_date}" | tee -a "$LOG_FILE"

# 检查源目录是否存在且不为空
if [[ ! -d "$SRCDIR" || -z "$(ls -A "$SRCDIR" 2>/dev/null)" ]]; then
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] 错误: 源目录不存在或为空，无法压缩" | tee -a "$LOG_FILE"
  exit 1
fi

tar -czf "$ARCHIVE_NAME" -C "$(dirname "$SRCDIR")" "$(basename "$SRCDIR")"

if [[ $? -eq 0 ]]; then
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] 压缩成功: $ARCHIVE_NAME" | tee -a "$LOG_FILE"

  # 计算MD5校验和
  md5sum "$ARCHIVE_NAME" > "${ARCHIVE_NAME}.md5"
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] 生成MD5校验和: ${ARCHIVE_NAME}.md5" | tee -a "$LOG_FILE"

  # 直接保留原始文件，不进行确认
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] 保留原始文件" | tee -a "$LOG_FILE"
else
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] 压缩失败" | tee -a "$LOG_FILE"
fi

echo "[$(date '+%Y-%m-%d %H:%M:%S')] 脚本执行完成" | tee -a "$LOG_FILE"