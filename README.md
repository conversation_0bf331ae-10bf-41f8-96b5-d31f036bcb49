# 5G基站数据下载脚本使用说明

## 新增功能

- **交互式日期输入**：脚本启动时提示用户输入日期，避免手动修改脚本导致的错误
- **日期确认机制**：用户可以确认输入的日期是否正确，不正确可重新输入
- **任务名称输入**：用户可以为每次下载任务指定名称，用于命名目录和压缩文件
- **格式验证**：对日期和任务名称进行格式验证，确保输入符合要求

## 脚本概述

`improved_script.sh`是一个高效的多线程SFTP下载脚本。该脚本具有以下特点：

- **多服务器并行下载**：同时连接多台服务器进行数据下载
- **多线程下载**：每个服务器连接支持多个并行任务
- **断点续传**：支持中断后继续下载
- **自动重试**：网络异常时自动重试
- **详细日志**：提供完整的下载过程日志和统计信息
- **性能优化**：针对大文件传输进行了多项性能优化

## 配置说明

使用前，可以根据需要修改脚本中的以下配置参数：

### 基本配置

- `USER`：SFTP用户名
- `PASSWORDLIST`：SFTP密码列表
- `DESDIRLIST`：服务器上的源目录列表

### 交互式输入

脚本启动时会提示用户输入以下信息：

- **日期**：要下载的数据日期，格式为YYYYMMDD（例如：20250409）
- **任务名称**：用于命名下载目录和压缩文件，只允许字母、数字和下划线

### 性能配置

- `MAX_PARALLEL_DOWNLOADS`：最大并行下载任务数（默认15）
- `MAX_PARALLEL_FILES`：每个LFTP会话的并行文件数（默认8）
- `TIMEOUT`：连接超时时间（秒）
- `CONN_RETRIES`：连接重试次数
- `TRANSFER_RATE_LIMIT`：传输速率限制（0表示不限制）

## 使用方法

### 基本用法

1. 确保脚本有执行权限：

```bash
chmod +x improved_script.sh
```

2. 运行脚本：

```bash
./improved_script.sh
```

3. 按提示输入信息：
   - 输入需要下载的数据日期（格式：YYYYMMDD）
   - 确认日期是否正确
   - 输入任务名称（只允许字母、数字和下划线）

### 使用Screen持续运行（推荐方式）

Screen是一个强大的终端多路复用器，非常适合长时间运行脚本。它允许在断开SSH连接后仍然保持脚本运行，并可以随时重新连接查看运行状态。

#### 安装Screen

```bash
# Debian/Ubuntu
sudo apt-get update
sudo apt-get install screen

# CentOS/RHEL
sudo yum install screen
```

#### 基本使用方法

```bash
# 创建新的screen会话
screen -S mr_download

# 在screen会话中运行脚本
./improved_script.sh
```

脚本开始运行后，可以使用 `Ctrl+A` 然后按 `D` 将screen分离（detach）。脚本会在后台继续运行，即使断开SSH连接。

#### 重新连接到运行中的会话

```bash
# 列出所有运行中的screen会话
screen -ls

# 重新连接到指定会话
screen -r mr_download
```

#### 常用Screen命令

- `Ctrl+A` 然后 `?` - 显示帮助
- `Ctrl+A` 然后 `D` - 分离当前会话
- `Ctrl+A` 然后 `C` - 创建新窗口
- `Ctrl+A` 然后 `N` - 切换到下一个窗口
- `Ctrl+A` 然后 `P` - 切换到上一个窗口
- `Ctrl+A` 然后 `K` - 关闭当前窗口

#### 创建持续运行的下载循环

如果希望脚本在完成后自动重新启动，可以创建一个循环脚本：

```bash
# 创建一个循环脚本
cat > loop_download.sh << 'EOF'
#!/bin/bash
while true; do
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] 启动下载脚本..."
  ./improved_script.sh
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] 脚本执行完成，等待一小时后重新启动"
  sleep 3600  # 等待一小时
 done
EOF

chmod +x loop_download.sh

# 在screen中运行循环脚本
screen -S mr_download_loop
./loop_download.sh
```

这样，脚本会在每次执行完成后等待一小时，然后自动重新启动。

#### 设置Screen自动启动

可以将Screen会话添加到系统启动脚本中，确保服务器重启后自动启动下载任务：

```bash
# 创建启动脚本
cat > start_download.sh << 'EOF'
#!/bin/bash
# 检查是否已有相同名称的screen会话
if ! screen -list | grep -q "mr_download"; then
  # 创建新的screen会话并运行下载脚本
  screen -dmS mr_download /path/to/improved_script.sh
  echo "已启动下载脚本"
else
  echo "下载脚本已在运行中"
fi
EOF

chmod +x start_download.sh

# 添加到crontab以在系统启动时运行
(crontab -l 2>/dev/null; echo "@reboot /path/to/start_download.sh") | crontab -
```

### 配置服务器列表

脚本支持从文件加载服务器IP列表。创建`server_list.txt`文件，每行一个IP地址：

```
*************
*************
*************
*************
```

### 配置基站节点列表

脚本支持从文件加载基站节点列表。创建`nodeb_list.txt`文件，每行一个节点名称：

```
成都北HR04H
武侯HR03H
锦江HR03H
成青羊HR04H
```

## 功能详解

### 1. 多服务器并行下载

脚本会并行测试所有服务器的连接，找出可用的服务器，然后同时从多台服务器下载数据。这大大提高了总体下载速度，特别是在网络带宽充足的情况下。

### 2. 智能密码尝试

脚本支持为每个服务器尝试多个密码，一旦找到有效密码就会记录并使用，无需用户干预。

### 3. 多线程下载

每个SFTP连接都使用多线程下载，包括：
- 多个并行文件同时下载
- 单个大文件的分段并行下载

### 4. 断点续传

如果下载过程中断，再次运行脚本时会自动跳过已成功下载的文件，继续下载未完成的文件。

### 5. 详细日志和统计

脚本提供详细的日志记录，包括：
- 每个下载任务的开始和结束时间
- 下载速度和文件大小统计
- 成功/失败/跳过的任务统计
- 图形化进度显示

### 6. 自动压缩和校验

下载完成后，脚本会：
1. 自动将下载的文件打包为tar.gz格式，文件名包含任务名称和日期
2. 生成MD5校验和文件
3. 可选择是否删除原始文件

## 日志文件

脚本会在`./logs`目录下生成带时间戳的日志文件，格式为：
```
download_log_YYYYMMDD_HHMMSS.txt
```

日志文件包含详细的下载过程信息，可用于排查问题或分析性能。

## 性能优化建议

1. 根据网络环境调整`MAX_PARALLEL_DOWNLOADS`和`MAX_PARALLEL_FILES`参数
2. 如果网络不稳定，增加`CONN_RETRIES`的值
3. 对于大文件，可以增加`--use-pget-n`参数的值（脚本中默认为3）
4. 如果需要限制带宽使用，可以设置`TRANSFER_RATE_LIMIT`参数

## 常见问题排查

### 无法连接到服务器

- 检查服务器IP地址是否正确
- 确认用户名和密码是否正确
- 检查网络连接和防火墙设置

### 下载速度慢

- 增加并行下载数和并行文件数
- 检查网络带宽限制
- 确认服务器负载情况

### 下载失败

- 查看日志文件中的错误信息
- 确认远程目录和文件是否存在
- 检查本地磁盘空间是否充足

## 脚本工作流程

1. 提示用户输入日期并确认
2. 提示用户输入任务名称
3. 初始化配置和日志
4. 并行测试所有服务器连接
5. 计算总下载任务数
6. 并行执行下载任务
7. 实时显示下载进度
8. 汇总下载统计信息
9. 使用任务名称和日期创建压缩文件并生成校验和
10. 询问是否删除原始文件

## 高级用法

### 添加新的服务器目录

在`DESDIRLIST`数组中添加新的目录路径，可以从多个服务器目录下载数据。

### 调整网络参数

脚本中的LFTP参数已经过优化，但可以根据具体网络环境进行调整：

```bash
set net:socket-buffer 33554432  # 32MB缓冲区
set net:timeout $TIMEOUT
set net:max-retries $CONN_RETRIES
```

### 使用定时任务（Cron）

如果希望脚本按照固定的时间表运行，可以使用Linux的cron服务。

#### 基本crontab设置

```bash
# 编辑当前用户的crontab
crontab -e

# 添加定时任务，每天凌晨2点执行
0 2 * * * /path/to/improved_script.sh > /path/to/logs/cron_download_$(date +\%Y\%m\%d).log 2>&1
```

#### 常用的crontab时间格式

- `0 2 * * *` - 每天凌晨2点
- `0 */6 * * *` - 每6小时执行一次
- `0 2 * * 1-5` - 工作日（周一至周五）凌晨2点
- `0 2 1 * *` - 每月第一天凌晨2点

#### 高级cron配置

注意：由于脚本现在需要交互式输入，如果要使用cron定时执行，需要修改脚本以支持命令行参数或使用expect等工具自动化输入。

例如，使用expect创建一个自动化脚本：

```bash
#!/usr/bin/expect

set date "20250409"
set task_name "daily_download"

spawn ./improved_script.sh

# 等待日期提示并输入
 expect "请输入需要下载的数据日期"
 send "$date\r"

# 等待确认提示并确认
 expect "这个日期是否正确"
 send "y\r"

# 等待任务名称提示并输入
 expect "请输入任务名称"
 send "$task_name\r"

# 等待脚本完成
 interact
```

然后在crontab中使用这个自动化脚本：

```bash
# 每天凌晨2点执行自动化脚本
0 2 * * * /path/to/auto_download.exp > /path/to/logs/download_$(date +\%Y\%m\%d).log 2>&1
```

#### 监控cron任务

可以设置额外的cron任务来监控下载任务的状态：

```bash
# 每小时检查一次下载状态，如果发现问题则发送邮件
0 * * * * if ! grep -q "下载成功" /path/to/logs/download_$(date +\%Y\%m\%d).log; then echo "下载任务可能出现问题，请检查" | mail -s "下载警告" <EMAIL>; fi
```

## 注意事项

1. 脚本默认使用预设的密码，请确保密码安全
2. 大量并行下载可能会占用大量网络带宽和系统资源
3. 请确保有足够的磁盘空间存储下载的文件和压缩文件
4. 任务名称只允许字母、数字和下划线，不允许空格和特殊字符
5. 日期格式必须为8位数字（YYYYMMDD），如果格式不正确脚本会要求重新输入
6. 如果需要在后台或定时执行，请参考使用expect等工具自动化输入
