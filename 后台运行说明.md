# 5G基站数据下载脚本后台运行说明

本文档介绍了如何在后台运行5G基站数据下载脚本，以便在断开SSH连接后仍能继续下载。

## 目录

1. [使用nohup在后台运行](#使用nohup在后台运行)
2. [使用screen在虚拟终端中运行](#使用screen在虚拟终端中运行)
3. [使用expect自动化输入](#使用expect自动化输入)
4. [使用crontab定时运行](#使用crontab定时运行)
5. [查看下载进度](#查看下载进度)
6. [常见问题排查](#常见问题排查)

## 使用nohup在后台运行

`run_download_background.sh`脚本使用`nohup`命令在后台运行下载脚本，即使关闭终端也能继续运行。

### 使用方法

```bash
# 给脚本添加执行权限
chmod +x run_download_background.sh

# 运行脚本，指定日期和任务名称
./run_download_background.sh 20250418 beijing_task

# 如果需要指定基站列表，可以添加第三个参数
./run_download_background.sh 20250418 beijing_task "成都北HR04H 武侯HR03H 锦江HR03H"
```

### 参数说明

- 第一个参数：日期，格式为YYYYMMDD
- 第二个参数：任务名称，只允许字母、数字和下划线
- 第三个参数（可选）：基站列表，多个基站用空格分隔

### 工作原理

1. 脚本创建一个临时文件，包含所有需要的输入
2. 使用`nohup`命令在后台运行下载脚本，将输入重定向到临时文件
3. 所有输出被重定向到日志文件
4. 下载完成后，临时文件会被自动删除

## 使用screen在虚拟终端中运行

`run_download_screen.sh`脚本使用`screen`命令在虚拟终端中运行下载脚本，可以随时重新连接查看进度。

### 安装screen

```bash
# Debian/Ubuntu
sudo apt-get install screen

# CentOS/RHEL
sudo yum install screen
```

### 使用方法

```bash
# 给脚本添加执行权限
chmod +x run_download_screen.sh

# 运行脚本，指定日期和任务名称
./run_download_screen.sh 20250418 beijing_task

# 如果需要指定基站列表，可以添加第三个参数
./run_download_screen.sh 20250418 beijing_task "成都北HR04H 武侯HR03H 锦江HR03H"
```

### 重新连接到screen会话

```bash
# 列出所有screen会话
screen -ls

# 重新连接到指定会话
screen -r download_beijing_task_20250418
```

### 常用screen快捷键

- `Ctrl+A` 然后 `D`：分离会话并返回到原始终端
- `Ctrl+A` 然后 `K`：终止会话
- `Ctrl+A` 然后 `?`：显示帮助

## 使用expect自动化输入

`run_download_expect.sh`脚本使用`expect`自动化输入，适合在定时任务中使用。

### 安装expect

```bash
# Debian/Ubuntu
sudo apt-get install expect

# CentOS/RHEL
sudo yum install expect
```

### 使用方法

```bash
# 给脚本添加执行权限
chmod +x run_download_expect.sh

# 运行脚本，指定日期和任务名称
./run_download_expect.sh 20250418 beijing_task

# 如果需要指定基站列表，可以添加第三个参数
./run_download_expect.sh 20250418 beijing_task "成都北HR04H 武侯HR03H 锦江HR03H"
```

### 工作原理

1. 脚本启动下载脚本
2. 自动响应所有提示并输入相应的值
3. 所有输出被重定向到日志文件

## 使用crontab定时运行

可以使用`crontab`定时运行下载脚本，例如每天凌晨自动下载数据。

### 编辑crontab

```bash
# 编辑当前用户的crontab
crontab -e
```

### 添加定时任务

```
# 每天凌晨2点运行下载任务
0 2 * * * /path/to/run_download_expect.sh 20250418 daily_download > /path/to/logs/cron_download_$(date +\%Y\%m\%d).log 2>&1
```

### 查看当前crontab设置

```bash
crontab -l
```

## 查看下载进度

无论使用哪种方式在后台运行脚本，都可以通过查看日志文件来跟踪下载进度。

```bash
# 实时查看日志文件
tail -f logs/download_beijing_task_20250418_20230101_120000.log
```

## 常见问题排查

### 脚本没有在后台运行

- 检查是否给脚本添加了执行权限
- 检查是否有足够的权限创建日志文件
- 检查系统是否有足够的资源运行脚本

### screen会话无法创建

- 检查screen是否已安装
- 检查是否有足够的权限创建screen会话
- 检查是否已经存在同名的screen会话

### expect脚本无法运行

- 检查expect是否已安装
- 检查脚本中的路径是否正确
- 检查是否给脚本添加了执行权限

### crontab任务没有运行

- 检查crontab语法是否正确
- 检查脚本路径是否正确
- 检查系统日志以查找cron错误
- 确保cron服务正在运行

---

*文档最后更新时间：2023年12月*
