# 每天凌晨2点运行下载任务
# 使用expect脚本自动化输入
0 2 * * * /path/to/run_download_expect.sh 20250418 daily_download > /path/to/logs/cron_download_$(date +\%Y\%m\%d).log 2>&1

# 每周一凌晨3点运行下载任务
# 使用nohup在后台运行
0 3 * * 1 /path/to/run_download_background.sh 20250418 weekly_download > /path/to/logs/cron_weekly_$(date +\%Y\%m\%d).log 2>&1

# 使用crontab的方法:
# 1. 编辑当前用户的crontab:
#    crontab -e
# 2. 添加上面的定时任务
# 3. 保存并退出编辑器
# 4. 查看当前的crontab设置:
#    crontab -l
