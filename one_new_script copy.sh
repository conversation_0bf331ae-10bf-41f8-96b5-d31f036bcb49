
#!/bin/bash
# 严格模式设置
set -euo pipefail

# 日志函数
log() {
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a download_log.txt
}

# 错误处理函数
handle_error() {
  log "错误发生在第 $1 行: $2"
  exit 1
}

# 设置错误处理
trap 'handle_error ${LINENO} "$BASH_COMMAND"' ERR

# 清理任务名称的函数
sanitize_task_name() {
  local task_name="$1"
  # 只允许字母、数字和下划线
  echo "$task_name" | tr -d '\r\n' | sed 's/[^a-zA-Z0-9_]/_/g'
}

# 提示用户输入日期
echo "请输入需要下载的数据日期(格式:YYYYMMDD,例如:20250418):"
read -r input_date

# 验证日期格式
while ! [[ $input_date =~ ^[0-9]{8}$ ]]; do
  echo "日期格式不正确,请重新输入(格式:YYYYMMDD):"
  read -r input_date
done

# 确认日期
current_date=$input_date
echo "您输入的日期是：$current_date"
echo "这个日期是否正确？ (y/n)"
read -r confirm

while [[ ! $confirm =~ ^[Yy]$ ]]; do
  echo "请重新输入日期(格式:YYYYMMDD):"
  read -r input_date

  # 验证日期格式
  while ! [[ $input_date =~ ^[0-9]{8}$ ]]; do
    echo "日期格式不正确,请重新输入(格式:YYYYMMDD):"
    read -r input_date
  done

  current_date=$input_date
  echo "您输入的日期是：$current_date"
  echo "这个日期是否正确？ (y/n)"
  read -r confirm
done

# 提示用户输入任务名称
echo "请输入任务名称（仅允许字母、数字和下划线，不允许空格和特殊字符）："
read -r task_name

# 验证任务名称
while ! [[ $task_name =~ ^[a-zA-Z0-9_]+$ ]]; do
  echo "任务名称格式不正确，只允许字母、数字和下划线，请重新输入："
  read -r task_name
done

# 清理任务名称
task_name=$(sanitize_task_name "$task_name")

# 下载文件名设置
ARCHIVE_NAME="${task_name}_5G_Station_PCIMR-${current_date}.tar.gz"

# 目标目录(本地目录)
SRCDIR="/home/<USER>/MR_get/${task_name}_5G_Station_PCIMR-$current_date"
mkdir -p "$SRCDIR"

# FTP目录(服务器文件目录)
DESDIRLIST=("/export/home/<USER>/internalftp/var/SauService/filter/TSInventory/gnodeb_sig")

log "开始下载任务: ${task_name}, 日期: $current_date 的数据"

# 清理站点名称的函数
sanitize_station_name() {
  local station_name="$1"
  echo "$station_name" | tr -d '\r\n' | sed 's/[^[:alnum:][:space:]一-龥]/_/g'
}

# 检查是否有节点列表文件
NODEB_LIST_FILE="nodeb_list.txt"
if [[ -f "$NODEB_LIST_FILE" ]]; then
  # 从文件读取站点列表，忽略注释和空行
  raw_nodeb_list=($(grep -v '^\s*#\|^\s*$' "$NODEB_LIST_FILE"))

  # 清理站点名称
  Nodeb_set=()
  for nodeb in "${raw_nodeb_list[@]}"; do
    clean_nodeb=$(sanitize_station_name "$nodeb")
    Nodeb_set+=("$clean_nodeb")
  done

  log "从文件加载并清理了 ${#Nodeb_set[@]} 个基站节点"
else
  # 这里保留原脚本的基站列表，但为了简洁只保留注释说明
  log "使用脚本内置的基站列表"
  # 初始化为空数组
  Nodeb_set=()
  # 如果需要手动输入基站列表，可以取消下面的注释
  echo "请手动输入基站列表，使用空格隔开："
  read -r -a raw_nodeb_list
  # 清理站点名称
  for nodeb in "${raw_nodeb_list[@]}"; do
    clean_nodeb=$(sanitize_station_name "$nodeb")
    Nodeb_set+=("$clean_nodeb")
  done
fi

# SFTP服务器IP列表文件
SERVER_LIST_FILE="server_list.txt"
log "尝试从 $SERVER_LIST_FILE 文件加载服务器IP列表"

# 清理IP地址的函数
sanitize_ip() {
  local ip="$1"
  echo "$ip" | tr -d '\r\n' | sed 's/[^0-9\.]//g'
}

if [[ -f "$SERVER_LIST_FILE" ]]; then
  # 从文件读取IP列表，忽略注释和空行
  raw_ip_list=($(grep -v '^\s*#\|^\s*$' "$SERVER_LIST_FILE"))

  # 清理和验证IP地址
  IPLIST=()
  invalid_ips=()
  for ip in "${raw_ip_list[@]}"; do
    clean_ip=$(sanitize_ip "$ip")
    # 简单验证IP格式
    if [[ $clean_ip =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
      IPLIST+=("$clean_ip")
    else
      invalid_ips+=("$ip")
    fi
  done

  log "从文件加载了 ${#IPLIST[@]} 个有效服务器IP"

  # 如果有无效IP，记录到日志
  if [[ ${#invalid_ips[@]} -gt 0 ]]; then
    log "警告: 发现 ${#invalid_ips[@]} 个无效IP地址"
    for invalid_ip in "${invalid_ips[@]}"; do
      log "无效IP: $invalid_ip"
    done
  fi

  # 如果没有有效IP，提示手动输入
  if [[ ${#IPLIST[@]} -eq 0 ]]; then
    log "文件中没有有效IP，请手动输入IP地址，使用空格隔开"
    read -r raw_input
    raw_ip_list=($raw_input)

    # 清理和验证手动输入的IP
    IPLIST=()
    for ip in "${raw_ip_list[@]}"; do
      clean_ip=$(sanitize_ip "$ip")
      if [[ $clean_ip =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
        IPLIST+=("$clean_ip")
      fi
    done

    log "已添加 ${#IPLIST[@]} 个有效服务器IP"
  fi
else
  log "未找到服务器列表文件 $SERVER_LIST_FILE"
  echo "请手动输入服务器IP地址，使用空格隔开："
  read -r raw_input
  raw_ip_list=($raw_input)

  # 清理和验证手动输入的IP
  IPLIST=()
  for ip in "${raw_ip_list[@]}"; do
    clean_ip=$(sanitize_ip "$ip")
    if [[ $clean_ip =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
      IPLIST+=("$clean_ip")
    fi
  done

  log "已添加 ${#IPLIST[@]} 个有效服务器IP"
fi

# 检查重复的IP地址
IPLIST=($(echo "${IPLIST[@]}" | tr ' ' '\n' | sort -u | tr '\n' ' '))
log "优化后的服务器IP数量: ${#IPLIST[@]}"

# 设置SFTP账号和密码
USER="ftpuser"
PASSWORDLIST=("Mk@83cka" "Mk@83cka23")

# 端口
PORT=22

# 连接超时时间(秒)
TIMEOUT=30

# 成功标记文件
SUCCESS_FILE="$SRCDIR/download_success.txt"
touch "$SUCCESS_FILE"

# 下载函数，减少重复代码
download_data() {
  local ip=$1
  local password=$2
  local desdir=$3
  local nodeb_dir=$4
  local target_path="$current_date"

  if [[ -n "$nodeb_dir" ]]; then
    target_path="$current_date/$nodeb_dir"
  fi

  # 检查是否已经成功下载过
  if grep -q "${ip}_${target_path}" "$SUCCESS_FILE"; then
    log "跳过已成功下载: $ip - $target_path"
    return 0
  fi

  log "尝试连接: $ip - $target_path"

  # 创建本地目录，确保与远程路径一致
  local local_dir="$SRCDIR/$target_path"
  log "创建本地目录: $local_dir"
  mkdir -p "$local_dir"

  # 设置超时
  timeout $TIMEOUT lftp -u "$USER,$password" "sftp://${ip}:${PORT}" 2>/dev/null <<EOF || return 1
  set sftp:connect-program "ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null"
  set net:timeout $TIMEOUT
  set net:max-retries 2
  set net:reconnect-interval-base 2
  cd "$desdir"
  lcd "$SRCDIR"
  # 打印调试信息
  !echo "[$(date '+%Y-%m-%d %H:%M:%S')] LFTP远程目录: $desdir, 本地目录: $SRCDIR" >> "download_log.txt"
  !echo "[$(date '+%Y-%m-%d %H:%M:%S')] 执行mirror命令: '$target_path/' -> '$target_path'" >> "download_log.txt"
  mirror --only-newer --parallel=8 --verbose=1 "$target_path/" "$target_path" >> "download_log.txt" 2>&1
  close
  quit
EOF

  # 检查下载结果
  if [[ $? -eq 0 ]]; then
    log "成功: $ip - $target_path"
    echo "${ip}_${target_path}" >> "$SUCCESS_FILE"
    return 0
  else
    log "失败: $ip - $target_path"
    return 1
  fi
}

# 主下载逻辑
download_count=0
success_count=0
failure_count=0

# 对每个服务器尝试连接
for IP in "${IPLIST[@]}"; do
  ip_connected=false

  # 尝试每个密码
  for PASSWORD in "${PASSWORDLIST[@]}"; do
    if $ip_connected; then
      break  # 已经连接成功，跳过其他密码
    fi

    # 尝试每个目标目录
    for DESDIR in "${DESDIRLIST[@]}"; do
      if $ip_connected; then
        break  # 已经连接成功，跳过其他目录
      fi

      log "尝试 IP:$IP 用户:$USER 目录:$DESDIR"

      # 测试连接
      if timeout $TIMEOUT lftp -u "$USER,$PASSWORD" "sftp://${IP}:${PORT}" -e "exit" 2>/dev/null; then
        ip_connected=true
        log "连接成功: $IP"

        # 根据Nodeb_set是否为空决定下载方式
        if [[ ${#Nodeb_set[@]} -eq 0 ]]; then
          log "下载整个日期目录: $current_date"
          download_data "$IP" "$PASSWORD" "$DESDIR" ""

          if [[ $? -eq 0 ]]; then
            success_count=$((success_count + 1))
          else
            failure_count=$((failure_count + 1))
          fi
          download_count=$((download_count + 1))
        else
          # 只下载指定的基站
          for NODEB_DIR in "${Nodeb_set[@]}"; do
            log "下载基站: $NODEB_DIR"
            download_data "$IP" "$PASSWORD" "$DESDIR" "$NODEB_DIR"

            if [[ $? -eq 0 ]]; then
              success_count=$((success_count + 1))
            else
              failure_count=$((failure_count + 1))
            fi
            download_count=$((download_count + 1))
          done
        fi
      fi
    done
  done

  if ! $ip_connected; then
    log "警告: 无法连接到服务器 $IP"
  fi
done

log "下载统计: 总共 $download_count, 成功 $success_count, 失败 $failure_count"

# 如果没有成功的下载，则退出
if [[ $success_count -eq 0 ]]; then
  log "错误: 没有成功的下载，退出"
  exit 1
fi

# 在压缩前检查空文件夹
log "开始检查空文件夹..."

# 定义检查空文件夹的函数
check_empty_dirs() {
  local dir="$1"
  local date_dir="$2"
  local empty_dirs=0
  local total_dirs=0
  local empty_dir_list="${dir}/empty_dirs.txt"

  # 初始化空文件夹列表
  : > "$empty_dir_list"

  # 检查站点文件夹是否为空
  for nodeb in "${Nodeb_set[@]}"; do
    local station_dir="$dir/$date_dir/$nodeb"

    # 如果站点目录存在，检查是否为空
    if [[ -d "$station_dir" ]]; then
      total_dirs=$((total_dirs + 1))

      # 检查目录是否为空
      if [[ -z "$(ls -A "$station_dir" 2>/dev/null)" ]]; then
        echo "$station_dir (站点: $nodeb)" >> "$empty_dir_list"
        empty_dirs=$((empty_dirs + 1))
      fi
    fi
  done

  # 返回空文件夹数量和总文件夹数量
  echo "$empty_dirs $total_dirs"
}

# 执行空文件夹检查
if [[ -d "$SRCDIR" && ${#Nodeb_set[@]} -gt 0 ]]; then
  log "检查站点文件夹是否为空..."
  # 打印调试信息
  log "源目录: $SRCDIR, 日期目录: $current_date, 站点数量: ${#Nodeb_set[@]}"
  empty_dirs_result=$(check_empty_dirs "$SRCDIR" "$current_date")
  empty_dirs_count=$(echo "$empty_dirs_result" | cut -d' ' -f1)
  total_dirs_count=$(echo "$empty_dirs_result" | cut -d' ' -f2)

  # 将空站点文件夹信息添加到统计中
  log "空站点文件夹: $empty_dirs_count/$total_dirs_count"

  # 如果有空站点文件夹，列出前10个
  if [[ $empty_dirs_count -gt 0 ]]; then
    log "空站点文件夹示例(前10个):"
    head -n 10 "$SRCDIR/empty_dirs.txt" | while read -r line; do
      log "  $line"
    done
    if [[ $empty_dirs_count -gt 10 ]]; then
      log "... 及其他 $((empty_dirs_count - 10)) 个空站点文件夹"
    fi
  fi
elif [[ ! -d "$SRCDIR" ]]; then
  log "警告: 无法检查空站点文件夹，目录 $SRCDIR 不存在"
else
  log "警告: 无法检查空站点文件夹，没有站点列表"
fi

# 将空站点文件夹信息添加到日志汇总
log "下载统计汇总: 成功=$success_count, 失败=$failure_count, 空站点文件夹=$empty_dirs_count/$total_dirs_count"

# 压缩下载的文件
log "开始压缩文件: $ARCHIVE_NAME"
log "任务名称: ${task_name}, 日期: ${current_date}"
tar -czf "$ARCHIVE_NAME" -C "$(dirname "$SRCDIR")" "$(basename "$SRCDIR")"

if [[ $? -eq 0 ]]; then
  log "压缩成功: $ARCHIVE_NAME"

  # 计算MD5校验和
  md5sum "$ARCHIVE_NAME" > "${ARCHIVE_NAME}.md5"
  log "生成MD5校验和: ${ARCHIVE_NAME}.md5"

  # 直接保留原始文件，不进行确认
  log "保留原始文件"
else
  log "压缩失败"
fi

log "脚本执行完成"