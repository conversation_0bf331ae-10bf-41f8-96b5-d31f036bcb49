#!/bin/bash
# LFTP断点续传功能示例

# 使用LFTP进行断点续传下载
resume_download() {
  local server="$1"
  local user="$2"
  local password="$3"
  local remote_path="$4"
  local local_path="$5"
  local timeout="$6"
  local log_file="$7"
  
  # 确保本地目录存在
  mkdir -p "$(dirname "$local_path")"
  
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] 尝试断点续传: $remote_path -> $local_path" >> "$log_file"
  
  # 使用LFTP的断点续传功能
  if timeout "$timeout" lftp -u "$user,$password" "sftp://${server}" 2>/dev/null <<EOF; then
  set sftp:connect-program "ssh -o StrictHostKeyChecking=no -o ServerAliveInterval=15"
  set net:timeout $timeout
  set net:max-retries 3
  set net:reconnect-interval-base 2
  set net:reconnect-interval-max 10
  set net:socket-buffer 33554432  # 32MB缓冲区
  set xfer:clobber on
  set xfer:disk-full-fatal true
  set xfer:log-file "$log_file"
  set pget:min-chunk-size 1M
  
  # 关键选项：使用mirror命令的断点续传功能
  # --continue: 继续之前的传输
  # --only-newer: 只传输比本地文件新的文件
  # --no-perms: 不设置权限
  # --no-umask: 不使用umask
  # --parallel=N: 并行下载N个文件
  # --use-pget-n=N: 每个文件使用N个连接
  mirror --continue --only-newer --no-perms --no-umask --parallel=8 --use-pget-n=3 "$remote_path" "$(dirname "$local_path")"
  
  close
  quit
EOF
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 断点续传成功: $remote_path" >> "$log_file"
    return 0
  else
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 断点续传失败: $remote_path" >> "$log_file"
    return 1
  fi
}

# 测试函数 - 这只是一个示例，实际环境中需要替换为真实的服务器信息
test_resume_download() {
  local log_file="resume_test.log"
  echo "测试断点续传功能" > "$log_file"
  
  # 这里需要替换为实际的服务器信息
  resume_download "example.com" "username" "password" "/remote/path" "./local/path" 30 "$log_file"
  
  echo "测试完成，查看日志: $log_file"
}

# 注意：这个测试函数需要实际的服务器信息才能运行
# test_resume_download
