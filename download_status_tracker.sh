#!/bin/bash
# 下载状态跟踪器

# 状态文件路径
STATUS_FILE="download_status.json"

# 初始化状态文件
initialize_status_file() {
  local task_name="$1"
  local date="$2"
  
  # 如果状态文件不存在，创建一个新的
  if [[ ! -f "$STATUS_FILE" ]]; then
    echo "{\"task_name\":\"$task_name\",\"date\":\"$date\",\"tasks\":[]}" > "$STATUS_FILE"
    echo "已初始化状态文件"
  else
    echo "状态文件已存在，将用于断点续传"
  fi
}

# 添加下载任务到状态文件
add_download_task() {
  local server="$1"
  local remote_path="$2"
  local local_path="$3"
  
  # 检查任务是否已存在
  if grep -q "\"remote_path\":\"$remote_path\"" "$STATUS_FILE"; then
    echo "任务已存在，跳过添加"
    return 0
  fi
  
  # 添加新任务
  local temp_file=$(mktemp)
  jq ".tasks += [{\"server\":\"$server\",\"remote_path\":\"$remote_path\",\"local_path\":\"$local_path\",\"status\":\"pending\",\"attempts\":0,\"last_attempt\":null}]" "$STATUS_FILE" > "$temp_file"
  mv "$temp_file" "$STATUS_FILE"
  echo "已添加下载任务: $remote_path"
}

# 更新任务状态
update_task_status() {
  local remote_path="$1"
  local status="$2"  # pending, in_progress, completed, failed
  
  local temp_file=$(mktemp)
  jq "(.tasks[] | select(.remote_path == \"$remote_path\")).status = \"$status\" | (.tasks[] | select(.remote_path == \"$remote_path\")).last_attempt = \"$(date +%s)\" | (.tasks[] | select(.remote_path == \"$remote_path\")).attempts += 1" "$STATUS_FILE" > "$temp_file"
  mv "$temp_file" "$STATUS_FILE"
  echo "已更新任务状态: $remote_path -> $status"
}

# 获取所有未完成的任务
get_pending_tasks() {
  jq -r '.tasks[] | select(.status != "completed") | "\(.server)|\(.remote_path)|\(.local_path)"' "$STATUS_FILE"
}

# 获取所有已完成的任务
get_completed_tasks() {
  jq -r '.tasks[] | select(.status == "completed") | "\(.server)|\(.remote_path)|\(.local_path)"' "$STATUS_FILE"
}

# 测试函数
test_status_tracker() {
  # 初始化状态文件
  initialize_status_file "test_task" "20230101"
  
  # 添加下载任务
  add_download_task "server1" "/path/to/file1" "/local/path/file1"
  add_download_task "server2" "/path/to/file2" "/local/path/file2"
  
  # 更新任务状态
  update_task_status "/path/to/file1" "in_progress"
  update_task_status "/path/to/file1" "completed"
  
  # 获取未完成的任务
  echo "未完成的任务:"
  get_pending_tasks
  
  # 获取已完成的任务
  echo "已完成的任务:"
  get_completed_tasks
  
  # 清理
  rm -f "$STATUS_FILE"
}

# 运行测试
# 注意：这需要jq工具，如果没有安装，请先安装
# apt-get install jq 或 yum install jq
if command -v jq >/dev/null 2>&1; then
  test_status_tracker
else
  echo "请先安装jq工具: apt-get install jq 或 yum install jq"
fi
