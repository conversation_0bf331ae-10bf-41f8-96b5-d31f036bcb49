#!/bin/bash
# 文件完整性检查函数

# 检查单个文件是否完整下载
check_file_integrity() {
  local local_file="$1"
  local remote_file="$2"
  local remote_size="$3"
  
  # 如果本地文件不存在，需要下载
  if [[ ! -f "$local_file" ]]; then
    echo "FILE_MISSING"
    return 1
  fi
  
  # 获取本地文件大小
  local local_size=$(stat -c %s "$local_file" 2>/dev/null || stat -f %z "$local_file" 2>/dev/null)
  
  # 比较文件大小
  if [[ "$local_size" -eq "$remote_size" ]]; then
    echo "FILE_COMPLETE"
    return 0
  else
    echo "FILE_INCOMPLETE"
    return 2
  fi
}

# 测试函数
test_check_file_integrity() {
  # 创建测试文件
  echo "测试内容" > test_file.txt
  local size=$(stat -c %s test_file.txt 2>/dev/null || stat -f %z test_file.txt 2>/dev/null)
  
  # 测试文件存在且大小相同
  result=$(check_file_integrity "test_file.txt" "remote/test_file.txt" "$size")
  echo "测试1 (文件完整): $result"
  
  # 测试文件存在但大小不同
  result=$(check_file_integrity "test_file.txt" "remote/test_file.txt" "$((size+10))")
  echo "测试2 (文件不完整): $result"
  
  # 测试文件不存在
  rm -f test_file.txt
  result=$(check_file_integrity "test_file.txt" "remote/test_file.txt" "$size")
  echo "测试3 (文件不存在): $result"
  
  # 清理
  rm -f test_file.txt
}

# 运行测试
test_check_file_integrity
