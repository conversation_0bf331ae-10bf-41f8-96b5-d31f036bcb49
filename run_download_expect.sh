#!/usr/bin/expect -f
# 使用expect自动化输入的下载脚本

# 检查参数
if {$argc < 2} {
    puts "用法: $argv0 <日期(YYYYMMDD)> <任务名称> \[基站列表(可选)\]"
    puts "示例: $argv0 20250418 beijing_task \"成都北HR04H 武侯HR03H 锦江HR03H\""
    exit 1
}

# 获取参数
set date [lindex $argv 0]
set task_name [lindex $argv 1]
set nodeb_list ""
if {$argc > 2} {
    set nodeb_list [lindex $argv 2]
}

# 创建日志目录
system mkdir -p logs

# 生成唯一的日志文件名
set timestamp [exec date +%Y%m%d_%H%M%S]
set log_file "logs/download_${task_name}_${date}_${timestamp}.log"

# 显示信息
puts "开始运行下载任务..."
puts "日期: $date"
puts "任务名称: $task_name"
if {$nodeb_list != ""} {
    puts "基站列表: $nodeb_list"
}
puts "日志文件: $log_file"

# 启动脚本
spawn ./one_new_script\ copy.sh

# 设置超时时间（秒）
set timeout 300

# 等待日期提示并输入
expect "请输入需要下载的数据日期"
send "$date\r"

# 等待确认提示并确认
expect "这个日期是否正确"
send "y\r"

# 等待任务名称提示并输入
expect "请输入任务名称"
send "$task_name\r"

# 如果提供了基站列表，则等待提示并输入
if {$nodeb_list != ""} {
    expect "请手动输入基站列表"
    send "$nodeb_list\r"
}

# 将输出重定向到日志文件
log_file -a $log_file

# 等待脚本完成
expect eof

puts "下载任务已启动，输出被重定向到 $log_file"
puts "可以使用 'tail -f $log_file' 命令查看下载进度"
