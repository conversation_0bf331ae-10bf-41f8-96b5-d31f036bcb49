# 5G基站数据下载脚本使用说明

![文档版本](https://img.shields.io/badge/版本-1.1-blue)
![更新日期](https://img.shields.io/badge/更新日期-2023年12月-green)

## 📋 目录

1. [功能概述](#功能概述)
2. [安装与配置](#安装与配置)
3. [基本使用方法](#基本使用方法)
4. [高级功能](#高级功能)
5. [常见问题排查](#常见问题排查)
6. [最佳实践](#最佳实践)
7. [注意事项](#注意事项)

---

## 功能概述

### 🔄 核心功能

`improved_script.sh`是一个高效的多线程SFTP下载工具，专为从多台服务器下载5G基站的MR数据而设计。

#### ✨ 主要特点

- **多服务器并行下载**：同时连接多台服务器进行数据下载
- **多线程下载**：每个服务器连接支持多个并行任务
- **断点续传**：支持中断后继续下载
- **自动重试**：网络异常时自动重试
- **详细日志**：提供完整的下载过程日志和统计信息
- **性能优化**：针对大文件传输进行了多项性能优化

#### 🆕 新增功能

- **增强的断点续传**：当网络中断或SSH断开后，重新启动脚本可以自动继续上次未完成的下载
- **文件完整性检查**：自动检测文件是否完整下载，只下载缺失或不完整的部分
- **空站点文件夹检查**：自动检查并统计下载目录中的空文件夹，并在日志中记录空文件夹列表
- **交互式日期输入**：脚本启动时提示用户输入日期，避免手动修改脚本导致的错误
- **日期确认机制**：用户可以确认输入的日期是否正确，不正确可重新输入
- **任务名称输入**：用户可以为每次下载任务指定名称，用于命名目录和压缩文件
- **格式验证**：对日期和任务名称进行格式验证，确保输入符合要求
- **特殊字符清理**：自动清理站点名称和IP地址中的特殊字符，避免路径问题
- **自动保留原始文件**：下载完成后自动保留原始文件，不再询问是否删除

---

## 安装与配置

### 📥 系统要求

- Linux操作系统
- bash 4.0+
- lftp（用于SFTP连接和下载）
- 基本的Linux命令行工具（如grep, sed, find等）

### 🔧 安装依赖

在Debian/Ubuntu系统上：

```bash
sudo apt-get update
sudo apt-get install lftp
```

在CentOS/RHEL系统上：

```bash
sudo yum install lftp
```

### ⚙️ 配置参数

使用前，可以根据需要修改脚本中的以下配置参数：

#### 基本配置

- `USER`：SFTP用户名
- `PASSWORDLIST`：SFTP密码列表
- `DESDIRLIST`：服务器上的源目录列表

#### 性能配置

- `MAX_PARALLEL_DOWNLOADS`：最大并行下载任务数（默认15）
- `MAX_PARALLEL_FILES`：每个LFTP会话的并行文件数（默认8）
- `TIMEOUT`：连接超时时间（秒）
- `CONN_RETRIES`：连接重试次数
- `TRANSFER_RATE_LIMIT`：传输速率限制（0表示不限制）

> ⚠️ **注意**：过高的并行下载数可能会导致网络拥塞或服务器负载过高。建议根据实际网络环境和服务器性能调整这些参数。

---

## 基本使用方法

### 🚀 快速开始

1. **确保脚本有执行权限**：

```bash
chmod +x improved_script.sh
```

2. **运行脚本**：

```bash
./improved_script.sh
```

3. **按提示输入信息**：
   - 输入需要下载的数据日期（格式：YYYYMMDD）
   - 确认日期是否正确
   - 输入任务名称（只允许字母、数字和下划线）

> 📝 **提示**：任务名称将用于创建下载目录和最终的压缩文件名，建议使用有意义的名称，如"北京基站_20231201"。

### 📋 配置文件

#### 服务器列表配置

脚本支持从文件加载服务器IP列表。创建`server_list.txt`文件，每行一个IP地址：

```
# 服务器IP列表
# 每行一个IP地址
# 以#开头的行将被视为注释
# 空行将被忽略

# 第一组服务器
*************
*************
*************
*************
```

> 🔎 **提示**：脚本会自动清理IP地址中的特殊字符，并验证IP格式。如果文件不存在或没有有效IP，脚本会提示手动输入。

#### 基站节点列表配置

脚本支持从文件加载基站节点列表。创建`nodeb_list.txt`文件，每行一个节点名称：

```
# 基站节点列表文件
# 每行一个基站名称
# 以#开头的行将被视为注释
# 空行将被忽略

成都北HR04H
武侯HR03H
锦江HR03H
成青羊HR04H
```

> 🔍 **说明**：脚本会自动清理站点名称中的特殊字符，避免路径问题。清理过程使用`sanitize_station_name`函数，它会将非字母、非数字、非空格和非中文字符替换为下划线。例如，`成都(北)HR04H`将被清理为`成都_北_HR04H`。

---

## 高级功能

### 🔄 断点续传功能

脚本具有强大的断点续传功能，当网络中断或SSH连接断开后：

- 重新启动脚本时会自动检测已下载的文件
- 对于完整下载的文件会自动跳过
- 对于部分下载的文件会从断点处继续下载
- 对于未开始下载的文件会重新下载

> ⭐ **最佳实践**：当网络中断后，使用相同的任务名称和日期重新启动脚本，以便断点续传功能正常工作。

### 📊 日志和统计

脚本提供详细的日志记录，包括：
- 每个下载任务的开始和结束时间
- 下载速度和文件大小统计
- 成功/失败/跳过的任务统计
- 空站点文件夹数量和列表
- 图形化进度显示

日志文件位于`./logs`目录，格式为：`download_log_YYYYMMDD_HHMMSS.txt`

> 🔎 **提示**：脚本会自动检测并统计以站点名称命名的空文件夹，这有助于发现可能的下载问题。如果空站点文件夹数量过多，可能表明服务器上的站点数据缺失或下载过程中出现了错误。

### 🗜️ 自动压缩和校验

下载完成后，脚本会：
1. 自动将下载的文件打包为tar.gz格式，文件名包含任务名称和日期
2. 生成MD5校验和文件
3. 自动保留原始文件（不再询问是否删除）

> 💡 **提示**：生成的MD5校验和文件可用于验证压缩文件的完整性，特别是在文件传输后。

### 🔧 参数调整

#### 断点续传参数

脚本使用LFTP的mirror命令进行断点续传，可以调整以下参数提高断点续传效率：

```bash
# 关键参数说明
mirror --continue         # 启用断点续传
       --only-newer       # 只传输比本地文件新的文件
       --parallel=8       # 并行下载8个文件
       --use-pget-n=3     # 每个文件使用3个连接并行下载
       --verbose=1        # 显示详细信息
```

> 🔧 **调优建议**：对于大文件，可以增加`--use-pget-n`的值；对于大量小文件，可以增加`--parallel`的值。

---

## 持续运行方案

### 🖥️ 使用Screen持续运行（推荐方式）

Screen是一个强大的终端多路复用器，非常适合长时间运行脚本。它允许在断开SSH连接后仍然保持脚本运行，并可以随时重新连接查看运行状态。

#### 安装Screen

```bash
# Debian/Ubuntu
sudo apt-get update
sudo apt-get install screen

# CentOS/RHEL
sudo yum install screen
```

#### 基本使用方法

```bash
# 创建新的screen会话
screen -S mr_download

# 在screen会话中运行脚本
./improved_script.sh
```

脚本开始运行后，可以使用 `Ctrl+A` 然后按 `D` 将screen分离（detach）。脚本会在后台继续运行，即使断开SSH连接。

#### 重新连接到运行中的会话

```bash
# 列出所有运行中的screen会话
screen -ls

# 重新连接到指定会话
screen -r mr_download
```

> ⚠️ **重要**：使用Screen是防止SSH断开导致下载中断的最佳方法。强烈建议在生产环境中使用。

#### 常用Screen命令

| 命令 | 功能 |
|------|------|
| `Ctrl+A` 然后 `?` | 显示帮助 |
| `Ctrl+A` 然后 `D` | 分离当前会话 |
| `Ctrl+A` 然后 `C` | 创建新窗口 |
| `Ctrl+A` 然后 `N` | 切换到下一个窗口 |
| `Ctrl+A` 然后 `P` | 切换到上一个窗口 |
| `Ctrl+A` 然后 `K` | 关闭当前窗口 |

### ⏱️ 使用定时任务（Cron）

如果希望脚本按照固定的时间表运行，可以使用Linux的cron服务。

#### 基本crontab设置

```bash
# 编辑当前用户的crontab
crontab -e

# 添加定时任务，每天凌晨2点执行
0 2 * * * /path/to/improved_script.sh > /path/to/logs/cron_download_$(date +\%Y\%m\%d).log 2>&1
```

> ⚠️ **注意**：由于脚本需要交互式输入，如果要使用cron定时执行，需要使用expect等工具自动化输入。

#### 使用expect自动化输入

```bash
#!/usr/bin/expect

set date "20250409"
set task_name "daily_download"

spawn ./improved_script.sh

# 等待日期提示并输入
 expect "请输入需要下载的数据日期"
 send "$date\r"

# 等待确认提示并确认
 expect "这个日期是否正确"
 send "y\r"

# 等待任务名称提示并输入
 expect "请输入任务名称"
 send "$task_name\r"

# 等待脚本完成
 interact
```

---

## 常见问题排查

### 🔍 问题诊断与解决方案

#### 无法连接到服务器

- 检查服务器IP地址是否正确
- 确认用户名和密码是否正确
- 检查网络连接和防火墙设置

#### 下载速度慢

- 增加并行下载数和并行文件数
- 检查网络带宽限制
- 确认服务器负载情况

#### 下载失败

- 查看日志文件中的错误信息
- 确认远程目录和文件是否存在
- 检查本地磁盘空间是否充足

#### 断点续传问题

- 如果断点续传不生效，检查日志文件中的错误信息
- 确认上次下载的目录结构是否完整
- 如果文件已损坏，尝试删除部分下载的文件并重新下载
- 检查服务器上的文件是否已更改

#### SSH连接断开

- 使用Screen或tmux在后台运行脚本，避免SSH断开影响下载
- 在服务器上设置SSH保持连接参数（ServerAliveInterval）
- 使用更稳定的网络连接

> 💡 **提示**：大多数下载问题可以通过查看日志文件来诊断。日志文件包含详细的错误信息和下载过程。

---

## 最佳实践

### 📈 性能优化建议

1. 根据网络环境调整`MAX_PARALLEL_DOWNLOADS`和`MAX_PARALLEL_FILES`参数
2. 如果网络不稳定，增加`CONN_RETRIES`的值
3. 对于大文件，可以增加`--use-pget-n`参数的值（脚本中默认为3）
4. 如果需要限制带宽使用，可以设置`TRANSFER_RATE_LIMIT`参数

### 🔄 工作流程优化

脚本的工作流程如下：

1. 检查是否存在上次中断的下载任务，如果有则准备断点续传
2. 提示用户输入日期并确认
3. 提示用户输入任务名称
4. 初始化配置和日志
5. 清理站点名称和IP地址中的特殊字符
6. 并行测试所有服务器连接
7. 计算总下载任务数
8. 对于断点续传，检查文件完整性并标记已完成的任务
9. 并行执行下载任务，对于未完成的文件使用断点续传
10. 实时显示下载进度
11. 汇总下载统计信息
12. 检查并统计空站点文件夹，记录到日志
13. 使用任务名称和日期创建压缩文件并生成校验和
14. 自动保留原始文件

> 🌟 **最佳实践**：对于大型下载任务，建议使用Screen在后台运行，并定期检查日志文件了解下载进度。

---

## 注意事项

### ⚠️ 重要提示

1. 脚本默认使用预设的密码，请确保密码安全
2. 大量并行下载可能会占用大量网络带宽和系统资源
3. 请确保有足够的磁盘空间存储下载的文件和压缩文件
4. 任务名称只允许字母、数字和下划线，不允许空格和特殊字符
5. 日期格式必须为8位数字（YYYYMMDD），如果格式不正确脚本会要求重新输入
6. 如果需要在后台或定时执行，请参考使用expect等工具自动化输入
7. 断点续传功能依赖于脚本生成的状态文件，请不要手动删除这些文件
8. 当网络中断后，建议使用相同的任务名称和日期重新启动脚本，以便断点续传
9. 脚本会自动清理站点名称和IP地址中的特殊字符，避免路径问题
10. 脚本现在会自动保留原始文件，不再询问是否删除

> 🚫 **禁止事项**：不要在下载过程中手动删除或修改下载目录中的文件，这可能会导致断点续传功能失效。

---

## 附录

### 📚 相关资源

- [LFTP官方文档](https://lftp.yar.ru/lftp-man.html)
- [Screen用户手册](https://www.gnu.org/software/screen/manual/screen.html)
- [Linux Crontab说明](https://man7.org/linux/man-pages/man5/crontab.5.html)

### 📞 支持与反馈

如有问题或建议，请联系系统管理员或脚本维护人员。

---

*文档最后更新时间：2023年12月*
